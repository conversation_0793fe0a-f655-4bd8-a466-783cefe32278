## Installation

```bash
pnpm add @tissue-dynamics/td-ui
```

> Note: You may need to authenticate with GitHub Packages. See: https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-npm-registry

## Usage

```svelte
<script>
	import { Button, ButtonVariant } from '@tissue-dynamics/td-ui';
</script>

<Button variant={ButtonVariant.PRIMARY}>Click me</Button>
```

## Development Workflow

```bash
# Install dependencies
pnpm install

# Start development
pnpm dev

# Run Storybook
pnpm storybook

# Build library
pnpm build
```

### Creating a Version Bump

After merging a change to `dev`, run:

```bash
pnpm changeset
```

Follow the prompts to choose the bump type and describe the change. This creates a `.changeset` entry.

### Preparing a Release

Before merging to `main`, run:

```bash
pnpm changeset version
```

This applies version bumps, updates `CHANGELOG.md`, and removes `.changeset` files. Commit the result and open a PR to `main`.

### Publishing

Publishing occurs automatically when changes are merged to `main` via GitHub Actions.

## Structure

```
src/lib/
├── components/
│   └── button/            # Button component
│       ├── button.svelte
│       ├── button.types.ts
│       ├── button.styles.ts
│       └── index.ts
├── types/                 # TypeScript types
├── hooks/                 # Hooks
├── utils/                 # Utility functions
└── index.ts               # Main exports
```

## Release Flow

| Branch | Purpose            | Action                      |
| ------ | ------------------ | --------------------------- |
| dev    | Active development | Feature branches merge here |
| main   | Stable releases    | Publish triggered here      |
