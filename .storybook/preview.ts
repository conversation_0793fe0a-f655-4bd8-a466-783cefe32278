import type { Preview } from '@storybook/sveltekit';
import '../src/app.css';
import TooltipProvider from '../src/lib/components/tooltip-provider/tooltip-provider.svelte';

const preview: Preview = {
	parameters: {
		controls: {
			matchers: {
				color: /(background|color)$/i,
				date: /Date$/i
			}
		}
	},
	decorators: [
		(story) => ({
			Component: TooltipProvider,
			props: {
				delayDuration: 200
			},
			slot: story
		})
	]
};

export default preview;
