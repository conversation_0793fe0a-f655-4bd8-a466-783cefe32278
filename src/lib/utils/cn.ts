import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Combines class names with smart merging for Tailwind CSS classes.
 *
 * This utility function combines the power of `clsx` for conditional class name
 * composition with `tailwind-merge` for intelligent merging of Tailwind CSS classes.
 * It resolves conflicts between Tailwind classes (e.g., `px-4 px-2` becomes `px-2`)
 * and handles conditional classes seamlessly.
 *
 * @param inputs - Class values to combine. Can be strings, objects, arrays, or any
 *                 combination that `clsx` supports, including conditional classes.
 * @returns A string of merged class names with Tailwind conflicts resolved
 *
 * @example
 * ```typescript
 * // Basic usage
 * cn('px-4', 'py-2'); // "px-4 py-2"
 *
 * // Conditional classes
 * cn('base-class', isActive && 'active-class'); // "base-class active-class" (if isActive is true)
 *
 * // Tailwind class conflicts are resolved
 * cn('px-4 px-2'); // "px-2" (last one wins)
 * cn('text-red-500', 'text-blue-500'); // "text-blue-500"
 *
 * // Object syntax for conditions
 * cn({
 *   'bg-blue-500': isPrimary,
 *   'bg-gray-500': !isPrimary,
 *   'text-white': true
 * }); // "bg-blue-500 text-white" (if isPrimary is true)
 *
 * // Array syntax
 * cn(['px-4', 'py-2'], isLarge && 'text-lg'); // "px-4 py-2 text-lg" (if isLarge is true)
 *
 * // Mixed usage (common in component libraries)
 * cn(
 *   'btn', // base class
 *   'px-4 py-2', // default padding
 *   {
 *     'bg-blue-500 text-white': variant === 'primary',
 *     'bg-gray-200 text-gray-800': variant === 'secondary',
 *   },
 *   size === 'large' && 'text-lg px-6 py-3',
 *   className // allow override from props
 * );
 * ```
 *
 * @see {@link https://github.com/lukeed/clsx} - For clsx documentation
 * @see {@link https://github.com/dcastil/tailwind-merge} - For tailwind-merge documentation
 */
export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// Re-export the type for convenience
export type { ClassValue };
