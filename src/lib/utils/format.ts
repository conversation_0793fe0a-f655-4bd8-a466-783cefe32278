/**
 * @fileoverview Comprehensive format utilities for UI components
 *
 * This module provides a collection of formatting functions for strings, numbers, dates,
 * and other common data types used in UI components. All functions are pure and stateless,
 * creating new Intl objects each time for predictable behavior.
 *
 * @example
 * ```typescript
 * import { formatCurrency, formatRelativeTime, capitalize } from '@your-lib/format';
 *
 * formatCurrency(1234.56); // "$1,234.56"
 * formatRelativeTime(new Date()); // "just now"
 * capitalize("hello world"); // "Hello world"
 * ```
 */

/**
 * String formatting utilities
 */

/**
 * Capitalizes the first letter of a string and converts the rest to lowercase.
 *
 * @param str - The string to capitalize
 * @returns The capitalized string, or the original string if empty/null
 *
 * @example
 * ```typescript
 * capitalize("hello"); // "Hello"
 * capitalize("WORLD"); // "World"
 * capitalize(""); // ""
 * ```
 */
export function capitalize(str: string): string {
	if (!str) return str;
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Converts a string to title case by capitalizing the first letter of each word.
 *
 * @param str - The string to convert to title case
 * @returns The title-cased string, or the original string if empty/null
 *
 * @example
 * ```typescript
 * titleCase("hello world"); // "Hello World"
 * titleCase("the quick brown fox"); // "The Quick Brown Fox"
 * titleCase(""); // ""
 * ```
 */
export function titleCase(str: string): string {
	if (!str) return str;
	return str
		.split(' ')
		.map((word) => capitalize(word))
		.join(' ');
}

/**
 * Converts a string to camelCase by removing spaces and capitalizing words after the first.
 *
 * @param str - The string to convert to camelCase
 * @returns The camelCased string, or the original string if empty/null
 *
 * @example
 * ```typescript
 * camelCase("hello world"); // "helloWorld"
 * camelCase("The Quick Brown Fox"); // "theQuickBrownFox"
 * camelCase("already-camelCase"); // "alreadyCamelCase"
 * ```
 */
export function camelCase(str: string): string {
	if (!str) return str;
	return str
		.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
			return index === 0 ? word.toLowerCase() : word.toUpperCase();
		})
		.replace(/\s+/g, '');
}

/**
 * Converts a string to kebab-case by replacing spaces and underscores with hyphens.
 *
 * @param str - The string to convert to kebab-case
 * @returns The kebab-cased string, or the original string if empty/null
 *
 * @example
 * ```typescript
 * kebabCase("hello world"); // "hello-world"
 * kebabCase("camelCaseString"); // "camel-case-string"
 * kebabCase("snake_case_string"); // "snake-case-string"
 * ```
 */
export function kebabCase(str: string): string {
	if (!str) return str;
	return str
		.replace(/([a-z])([A-Z])/g, '$1-$2')
		.replace(/[\s_]+/g, '-')
		.toLowerCase();
}

/**
 * Truncates text to a specified length and adds a suffix (ellipsis by default).
 *
 * @param text - The text to truncate
 * @param length - The maximum length of the text (excluding suffix)
 * @param suffix - The suffix to add when truncating (default: "...")
 * @returns The truncated text with suffix, or original text if shorter than length
 *
 * @example
 * ```typescript
 * truncate("Hello world", 5); // "Hello..."
 * truncate("Short", 10); // "Short"
 * truncate("Custom suffix", 6, "…"); // "Custom…"
 * ```
 */
export function truncate(text: string, length: number, suffix = '...'): string {
	if (!text || text.length <= length) return text;
	return text.substring(0, length).trim() + suffix;
}

/**
 * Truncates text at word boundaries to avoid breaking words in the middle.
 *
 * @param text - The text to truncate
 * @param wordCount - The maximum number of words to include
 * @param suffix - The suffix to add when truncating (default: "...")
 * @returns The truncated text with suffix, or original text if word count is within limit
 *
 * @example
 * ```typescript
 * truncateWords("The quick brown fox", 2); // "The quick..."
 * truncateWords("Hello world", 5); // "Hello world"
 * truncateWords("One two three four", 3, "…"); // "One two three…"
 * ```
 */
export function truncateWords(text: string, wordCount: number, suffix = '...'): string {
	if (!text) return text;
	const words = text.split(' ');
	if (words.length <= wordCount) return text;
	return words.slice(0, wordCount).join(' ') + suffix;
}

/**
 * Removes HTML tags from a string, leaving only the text content.
 *
 * @param html - The HTML string to strip tags from
 * @returns The string with HTML tags removed, or the original string if empty/null
 *
 * @example
 * ```typescript
 * stripHtml("<p>Hello <strong>world</strong></p>"); // "Hello world"
 * stripHtml("<div>No tags here</div>"); // "No tags here"
 * stripHtml("Plain text"); // "Plain text"
 * ```
 */
export function stripHtml(html: string): string {
	if (!html) return html;
	return html.replace(/<[^>]*>/g, '');
}

/**
 * Converts a string to a URL-friendly slug by removing special characters and spaces.
 *
 * @param text - The text to convert to a slug
 * @returns The slugified string, or the original string if empty/null
 *
 * @example
 * ```typescript
 * slugify("Hello World!"); // "hello-world"
 * slugify("The Quick & Brown Fox"); // "the-quick-brown-fox"
 * slugify("  Multiple   Spaces  "); // "multiple-spaces"
 * ```
 */
export function slugify(text: string): string {
	if (!text) return text;
	return text
		.toLowerCase()
		.trim()
		.replace(/[^\w\s-]/g, '')
		.replace(/[\s_-]+/g, '-')
		.replace(/^-+|-+$/g, '');
}

/**
 * Number formatting utilities
 */

/**
 * Formats a number as currency using the Intl.NumberFormat API.
 *
 * @param amount - The number to format as currency
 * @param currency - The currency code (ISO 4217) (default: "USD")
 * @param locale - The locale for formatting (default: "en-US")
 * @returns The formatted currency string
 *
 * @example
 * ```typescript
 * formatCurrency(1234.56); // "$1,234.56"
 * formatCurrency(1000, "EUR", "de-DE"); // "1.000,00 €"
 * formatCurrency(999.99, "JPY", "ja-JP"); // "¥1,000"
 * ```
 */
export function formatCurrency(amount: number, currency = 'USD', locale = 'en-US'): string {
	return new Intl.NumberFormat(locale, { style: 'currency', currency }).format(amount);
}

/**
 * Formats a number as a percentage with specified decimal places.
 *
 * @param value - The decimal value to format as percentage (e.g., 0.1234 for 12.34%)
 * @param decimals - The number of decimal places to show (default: 2)
 * @param locale - The locale for formatting (default: "en-US")
 * @returns The formatted percentage string
 *
 * @example
 * ```typescript
 * formatPercentage(0.1234); // "12.34%"
 * formatPercentage(0.5, 0); // "50%"
 * formatPercentage(0.123456, 3); // "12.346%"
 * ```
 */
export function formatPercentage(value: number, decimals = 2, locale = 'en-US'): string {
	return new Intl.NumberFormat(locale, {
		style: 'percent',
		minimumFractionDigits: decimals,
		maximumFractionDigits: decimals
	}).format(value);
}

/**
 * Formats a number with thousands separators and locale-specific formatting.
 *
 * @param value - The number to format
 * @param locale - The locale for formatting (default: "en-US")
 * @param options - Additional Intl.NumberFormat options
 * @returns The formatted number string
 *
 * @example
 * ```typescript
 * formatNumber(1234567.89); // "1,234,567.89"
 * formatNumber(1234567.89, "de-DE"); // "1.234.567,89"
 * formatNumber(123.456, "en-US", { maximumFractionDigits: 2 }); // "123.46"
 * ```
 */
export function formatNumber(
	value: number,
	locale = 'en-US',
	options?: Intl.NumberFormatOptions
): string {
	return new Intl.NumberFormat(locale, options).format(value);
}

/**
 * Formats byte values into human-readable file sizes (B, KB, MB, GB, etc.).
 *
 * @param bytes - The number of bytes to format
 * @param decimals - The number of decimal places to show (default: 2)
 * @returns The formatted file size string
 *
 * @example
 * ```typescript
 * formatFileSize(1024); // "1.00 KB"
 * formatFileSize(1048576); // "1.00 MB"
 * formatFileSize(1234567890, 1); // "1.1 GB"
 * formatFileSize(0); // "0 Bytes"
 * ```
 */
export function formatFileSize(bytes: number, decimals = 2): string {
	if (bytes === 0) return '0 Bytes';

	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));

	return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
}

/**
 * Formats large numbers with compact notation using K, M, B, T suffixes.
 *
 * @param num - The number to format
 * @param decimals - The number of decimal places to show (default: 1)
 * @returns The formatted compact number string
 *
 * @example
 * ```typescript
 * formatCompactNumber(1234); // "1.2K"
 * formatCompactNumber(1234567); // "1.2M"
 * formatCompactNumber(1234567890); // "1.2B"
 * formatCompactNumber(999); // "999"
 * ```
 */
export function formatCompactNumber(num: number, decimals = 1): string {
	if (num < 1000) return num.toString();

	const suffixes = ['K', 'M', 'B', 'T'];
	const tier = Math.floor(Math.log10(Math.abs(num)) / 3);

	if (tier === 0) return num.toString();

	const suffix = suffixes[tier - 1];
	const scale = Math.pow(10, tier * 3);
	const scaled = num / scale;

	return scaled.toFixed(decimals).replace(/\.0$/, '') + suffix;
}

/**
 * Date formatting utilities
 */

/**
 * Formats a date as relative time (e.g., "2 hours ago", "in 3 days").
 *
 * @param date - The date to format (Date object, string, or timestamp)
 * @param locale - The locale for formatting (default: "en-US")
 * @returns The formatted relative time string
 *
 * @example
 * ```typescript
 * formatRelativeTime(new Date(Date.now() - 3600000)); // "1 hour ago"
 * formatRelativeTime(new Date(Date.now() + 86400000)); // "in 1 day"
 * formatRelativeTime("2023-01-01"); // "11 months ago" (depending on current date)
 * ```
 */
export function formatRelativeTime(date: Date | string | number, locale = 'en-US'): string {
	const now = new Date();
	const target = new Date(date);
	const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);

	const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

	const intervals = [
		{ label: 'year', seconds: 31536000 },
		{ label: 'month', seconds: 2592000 },
		{ label: 'week', seconds: 604800 },
		{ label: 'day', seconds: 86400 },
		{ label: 'hour', seconds: 3600 },
		{ label: 'minute', seconds: 60 },
		{ label: 'second', seconds: 1 }
	];

	for (const interval of intervals) {
		const count = Math.floor(Math.abs(diffInSeconds) / interval.seconds);
		if (count >= 1) {
			return rtf.format(
				diffInSeconds < 0 ? count : -count,
				interval.label as Intl.RelativeTimeFormatUnit
			);
		}
	}

	return rtf.format(0, 'second');
}

/**
 * Formats a date using the Intl.DateTimeFormat API with customizable options.
 *
 * @param date - The date to format (Date object, string, or timestamp)
 * @param options - Intl.DateTimeFormat options for customizing the output
 * @param locale - The locale for formatting (default: "en-US")
 * @returns The formatted date string
 *
 * @example
 * ```typescript
 * formatDate(new Date()); // "12/25/2023" (default US format)
 * formatDate(new Date(), { dateStyle: "full" }); // "Monday, December 25, 2023"
 * formatDate(new Date(), { month: "long", day: "numeric" }); // "December 25"
 * ```
 */
export function formatDate(
	date: Date | string | number,
	options?: Intl.DateTimeFormatOptions,
	locale = 'en-US'
): string {
	return new Intl.DateTimeFormat(locale, options).format(new Date(date));
}

/**
 * Formats a duration in seconds to a human-readable string (e.g., "2h 30m 45s").
 *
 * @param seconds - The duration in seconds
 * @returns The formatted duration string
 *
 * @example
 * ```typescript
 * formatDuration(30); // "30s"
 * formatDuration(150); // "2m 30s"
 * formatDuration(3665); // "1h 1m 5s"
 * formatDuration(0); // "0s"
 * ```
 */
export function formatDuration(seconds: number): string {
	if (seconds < 60) return `${Math.floor(seconds)}s`;
	if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${Math.floor(seconds % 60)}s`;

	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const remainingSeconds = Math.floor(seconds % 60);

	return `${hours}h ${minutes}m ${remainingSeconds}s`;
}

/**
 * Contact formatting utilities
 */

/**
 * Formats a phone number according to the specified format.
 * Currently supports US format only.
 *
 * @param phoneNumber - The phone number string to format
 * @param format - The format to use (default: "US")
 * @returns The formatted phone number, or original string if format doesn't match
 *
 * @example
 * ```typescript
 * formatPhoneNumber("1234567890"); // "(*************"
 * formatPhoneNumber("************"); // "(*************"
 * formatPhoneNumber("invalid"); // "invalid"
 * ```
 */
export function formatPhoneNumber(phoneNumber: string, format = 'US'): string {
	const cleaned = phoneNumber.replace(/\D/g, '');

	if (format === 'US') {
		const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
		if (match) {
			return `(${match[1]}) ${match[2]}-${match[3]}`;
		}
	}

	return phoneNumber;
}

/**
 * Masks sensitive information by showing only the first and last few characters.
 * Useful for credit card numbers, SSNs, API keys, etc.
 *
 * @param value - The sensitive value to mask
 * @param visibleStart - Number of characters to show at the beginning (default: 4)
 * @param visibleEnd - Number of characters to show at the end (default: 4)
 * @param maskChar - Character to use for masking (default: "*")
 * @returns The masked string, or original string if too short to mask
 *
 * @example
 * ```typescript
 * maskSensitive("1234567890123456"); // "1234********3456"
 * maskSensitive("sensitive-data", 2, 2, "●"); // "se●●●●●●●●●●ta"
 * maskSensitive("short"); // "short" (too short to mask)
 * ```
 */
export function maskSensitive(
	value: string,
	visibleStart = 4,
	visibleEnd = 4,
	maskChar = '*'
): string {
	if (!value || value.length <= visibleStart + visibleEnd) return value;

	const start = value.substring(0, visibleStart);
	const end = value.substring(value.length - visibleEnd);
	const masked = maskChar.repeat(value.length - visibleStart - visibleEnd);

	return start + masked + end;
}

/**
 * Miscellaneous formatting utilities
 */

/**
 * Formats a number as an ordinal (1st, 2nd, 3rd, etc.).
 *
 * @param num - The number to format as ordinal
 * @returns The formatted ordinal string
 *
 * @example
 * ```typescript
 * formatOrdinal(1); // "1st"
 * formatOrdinal(2); // "2nd"
 * formatOrdinal(3); // "3rd"
 * formatOrdinal(11); // "11th"
 * formatOrdinal(21); // "21st"
 * ```
 */
export function formatOrdinal(num: number): string {
	const suffixes = ['th', 'st', 'nd', 'rd'];
	const remainder = num % 100;

	return num + (suffixes[(remainder - 20) % 10] || suffixes[remainder] || suffixes[0]);
}

/**
 * Formats a list of items with proper conjunctions and punctuation.
 *
 * @param items - Array of strings to format as a list
 * @param conjunction - The conjunction to use before the last item (default: "and")
 * @returns The formatted list string
 *
 * @example
 * ```typescript
 * formatList(["apple", "banana", "cherry"]); // "apple, banana, and cherry"
 * formatList(["red", "blue"], "or"); // "red or blue"
 * formatList(["single"]); // "single"
 * formatList([]); // ""
 * ```
 */
export function formatList(items: string[], conjunction = 'and'): string {
	if (items.length === 0) return '';
	if (items.length === 1) return items[0];
	if (items.length === 2) return `${items[0]} ${conjunction} ${items[1]}`;

	const lastItem = items[items.length - 1];
	const otherItems = items.slice(0, -1);

	return `${otherItems.join(', ')}, ${conjunction} ${lastItem}`;
}

/**
 * Extracts and formats initials from a full name.
 *
 * @param name - The full name to extract initials from
 * @param maxInitials - Maximum number of initials to return (default: 2)
 * @returns The formatted initials string
 *
 * @example
 * ```typescript
 * formatInitials("John Doe"); // "JD"
 * formatInitials("Mary Jane Watson", 3); // "MJW"
 * formatInitials("Madonna"); // "M"
 * formatInitials(""); // ""
 * ```
 */
export function formatInitials(name: string, maxInitials = 2): string {
	if (!name) return '';

	const words = name.trim().split(/\s+/);
	const initials = words.map((word) => word.charAt(0).toUpperCase()).slice(0, maxInitials);

	return initials.join('');
}

/**
 * Formats and normalizes a hex color code.
 * Converts 3-digit hex codes to 6-digit format and ensures proper # prefix.
 *
 * @param color - The hex color code to format
 * @returns The formatted hex color code, or original string if empty/null
 *
 * @example
 * ```typescript
 * formatHexColor("#abc"); // "#aabbcc"
 * formatHexColor("def"); // "#def"
 * formatHexColor("#123456"); // "#123456"
 * formatHexColor(""); // ""
 * ```
 */
export function formatHexColor(color: string): string {
	if (!color) return color;

	const hex = color.replace('#', '');
	if (hex.length === 3) {
		return `#${hex
			.split('')
			.map((c) => c + c)
			.join('')}`;
	}

	return `#${hex}`;
}
