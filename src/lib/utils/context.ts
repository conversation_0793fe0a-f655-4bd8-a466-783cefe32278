import { getContext, has<PERSON>ontext, setContext } from 'svelte';
import type { Getter as ContextGetter } from '$lib/types/generic.js';

/**
 * A function that sets a context value.
 *
 * @template T - The type of the context value.
 * @template Args - The type of arguments passed to the constructor/function
 * @returns The context value that was set
 */
export type ContextSetter<T, Args extends unknown[] = []> = (...args: Args) => T;

/**
 * Checks if a value is a class constructor.
 *
 * @template T - The type of the class instance
 * @param value - The value to check
 * @returns true if the value is a class constructor
 */
function isClassConstructor<T>(value: unknown): value is new (...args: unknown[]) => T {
	return typeof value === 'function' && /^\s*class\s+/.test(value.toString());
}

/**
 * Validates that a context value is valid.
 *
 * @param value - The value to validate
 * @throws Error if the value is invalid
 */
function validateContextValue(value: unknown): void {
	if (value === undefined || value === null) {
		throw new Error('Context value cannot be undefined or null');
	}
}

/**
 * Creates a pair of context getter and setter functions with type safety
 * for factory functions or class constructors.
 *
 * @template T The type of the context value.
 * @template Args The type of arguments passed to the factory function or constructor.
 * @param namespace A unique string identifier for the context.
 * @param contextValue The factory function or class constructor.
 * @param initialArgs Optional arguments to initialize the context immediately.
 * @returns A tuple containing the getter and setter functions. `setter` takes arguments defined by `Args`.
 */
export function createContext<T, Args extends unknown[]>(
	namespace: string,
	contextValue: ((...args: Args) => T) | (new (...args: Args) => T),
	initialArgs?: Args
): [ContextGetter<T>, ContextSetter<T, Args>];

/**
 * Creates a pair of context getter and setter functions with type safety
 * for plain values or objects.
 *
 * @template T The type of the context value (cannot be a function or class).
 * @param namespace A unique string identifier for the context.
 * @param contextValue The plain value or object to be used as context.
 * @returns A tuple containing the getter and setter functions. `setter` takes no arguments.
 */
export function createContext<T>(
	namespace: string,
	contextValue: T
): [ContextGetter<T>, ContextSetter<T, []>];

/**
 * Implementation for createContext overloads.
 * This is a type-safe wrapper around Svelte's context API.
 *
 * @example
 * ```ts
 * // For plain values/objects
 * const [getUser, setUser] = createContext('user', { name: 'John' });
 * setUser(); // No arguments allowed
 * const user = getUser();
 *
 * // For classes with options object
 * class UserService {
 *   constructor(public options: { name: string }) {}
 * }
 * const [getUserService, setUserService] = createContext('userService', UserService);
 * setUserService({ name: 'John' }); // Argument required
 * const service = getUserService();
 *
 * // For classes with multiple args and initial values
 * class UserService2 {
 *   constructor(public name: string, public age: number) {}
 * }
 * const [getUserService2, setUserService2] = createContext('userService2', UserService2, ['John', 30]);
 * setUserService2('Jane', 25); // Arguments required
 * const service2 = getUserService2();
 *
 * // For factory functions
 * const [getUserFactory, setUserFactory] = createContext('userFactory', (name: string) => ({ name }));
 * setUserFactory('John'); // Argument required
 * const userFromFactory = getUserFactory();
 * ```
 */
export function createContext<T, Args extends unknown[]>(
	namespace: string,
	contextValue: T | ((...args: Args) => T) | (new (...args: Args) => T),
	initialArgs?: Args
): [ContextGetter<T>, ContextSetter<T, Args | []>] {
	if (!namespace) {
		throw new Error('Namespace cannot be empty');
	}

	if (typeof contextValue !== 'function') {
		validateContextValue(contextValue);
	}

	const contextKey = Symbol(namespace);

	const getContextValue = (() => {
		if (!hasContext(contextKey)) {
			throw new Error(
				`Context for "${namespace}" not found. Make sure to call setContext() before calling getContext().`
			);
		}
		return getContext<T>(contextKey);
	}) as ContextGetter<T>;

	const setContextValue = ((...args: Args) => {
		let value: T;
		if (isClassConstructor<T>(contextValue)) {
			value = new contextValue(...args);
		} else if (typeof contextValue === 'function') {
			value = (contextValue as (...innerArgs: Args) => T)(...args);
		} else {
			if (args.length > 0) {
				throw new Error(
					`Context "${namespace}" created with a plain value does not accept arguments for its setter.`
				);
			}
			value = contextValue;
		}

		validateContextValue(value);
		setContext(contextKey, value);
		return value;
	}) as ContextSetter<T, Args>;

	if (initialArgs !== undefined) {
		setContextValue(...initialArgs);
	}

	return [getContextValue, setContextValue as ContextSetter<T, Args | []>];
}
