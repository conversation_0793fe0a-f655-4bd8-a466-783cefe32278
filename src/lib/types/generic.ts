/**
 * A type that represents a getter function.
 * When you want to pass a state rune to a function, you can use this type.
 *
 * @template T The type of the value returned by the state rune.
 * @example
 * ```ts
 * const state = $state(0);
 * const formatedState = format(() => state);
 *
 * @see {@link https://svelte.dev/docs/svelte/$state#Passing-state-into-functions}
 * ```
 */
export type Getter<T> = () => T;
