import type { Getter } from '$lib/types/generic.js';
import { untrack } from 'svelte';

/**
 * Implementation of beforeMount using runes
 *
 * This is a utility function that wraps Svelte's $effect.pre rune to provide a familiar
 * interface for developers coming from other frameworks or Svelte's older lifecycle hooks.
 *
 * This function is called before the component is mounted to the DOM.
 * It can return a cleanup function that will be called when the component is destroyed.
 *
 * @implementation
 * ```ts
 * $effect.pre(() => {
 *   untrack(() => {
 *     cleanup = fn();
 *   });
 *   return cleanup;
 * });
 * ```
 *
 * @example
 * ```svelte
 * <script>
 *   // Initialize some state before the component is mounted
 *   beforeMount(() => {
 *     // This runs before the component is mounted to the DOM
 *     console.log('Component will mount soon');
 *
 *     // Return a cleanup function (optional)
 *     return () => {
 *       console.log('Component is being destroyed');
 *     };
 *   });
 * </script>
 * ```
 *
 * @see https://svelte.dev/docs/svelte/$effect#$effect.pre
 * @param fn The synchronous function to call before the component is mounted
 */
export function beforeMount(fn: () => void | (() => void)) {
	let cleanup: (() => void) | void;
	$effect.pre(() => {
		untrack(() => {
			cleanup = fn();
		});
		return cleanup;
	});
}

/**
 * Implementation of onMount using runes for synchronous functions
 *
 * This is a utility function that wraps Svelte's $effect rune to provide a familiar
 * interface for developers coming from other frameworks or Svelte's older lifecycle hooks.
 *
 * This function is called when the component is mounted to the DOM.
 * It can return a cleanup function that will be called when the component is destroyed.
 *
 * @implementation
 * ```ts
 * $effect(() => {
 *   untrack(() => {
 *     cleanup = fn();
 *   });
 *   return cleanup;
 * });
 * ```
 *
 * @example
 * ```svelte
 * <script>
 *   // Run code when the component is mounted
 *   onMount(() => {
 *     // This runs after the component is mounted to the DOM
 *     console.log('Component is now mounted');
 *
 *     // Return a cleanup function (optional)
 *     return () => {
 *       console.log('Component is being destroyed');
 *     };
 *   });
 * </script>
 * ```
 *
 * @see https://svelte.dev/docs/svelte/$effect
 * @param fn The synchronous function to call when the component is mounted
 */
export function onMount(fn: () => void | Promise<void> | (() => void)) {
	let cleanup: (() => void) | void;
	$effect(() => {
		untrack(() => {
			const result = fn();

			// If the function returns a Promise, we need to handle it asynchronously
			if (result instanceof Promise) {
				// For async functions, we don't set cleanup since we can't await in $effect
				// The async function will run but we can't return a cleanup from it
				result.catch(console.error); // Handle any errors
			} else {
				// For sync functions, the result is either void or a cleanup function
				cleanup = result;
			}
		});
		return cleanup;
	});
}

/**
 * Implementation of onDestroy using runes
 *
 * This is a utility function that wraps Svelte's $effect rune to provide a familiar
 * interface for developers coming from other frameworks or Svelte's older lifecycle hooks.
 *
 * This function is called when the component is destroyed.
 * Note: For async cleanup functions, the Promise will not be awaited. This is essentially
 * a "fire and forget" implementation - the cleanup function will be called, but the
 * component destruction process will not wait for it to complete.
 *
 * @implementation
 * ```ts
 * $effect(() => {
 *   return cleanup;
 * });
 * ```
 *
 * @example
 * ```svelte
 * <script>
 *   import { onDestroy } from '@kitplus/runes';
 *
 *   // Run cleanup code when the component is destroyed
 *   onDestroy(() => {
 *     // This runs when the component is being destroyed
 *     console.log('Component is being destroyed');
 *
 *     // Clean up resources
 *     clearInterval(timer);
 *     removeEventListeners();
 *   });
 *
 *   // You can also use async cleanup
 *   onDestroy(async () => {
 *     // Async cleanup
 *     await saveState();
 *     await closeConnections();
 *   });
 * </script>
 * ```
 *
 * @see https://svelte.dev/docs/svelte/$effect
 * @param cleanup The function to call when the component is destroyed
 */
export function onDestroy(cleanup: () => void | Promise<void>) {
	$effect(() => {
		return cleanup;
	});
}

/**
 * Implementation of useEffect using runes, mimicking React's useEffect behavior with signal dependencies.
 *
 * Runs the effect function after the component renders and whenever any of the specified signal dependencies change.
 *
 * @example
 * ```svelte
 * <script>
 *   import { useEffect } from './runes.svelte'; // Adjust import path
 *   let count = $state(0);
 *   let doubled = $derived(count * 2);
 *
 *   useEffect(() => {
 *     // This effect runs when count changes
 *     console.log('Effect running for count:', count);
 *
 *     // Return a cleanup function (optional)
 *     return () => {
 *       console.log('Cleaning up effect for count');
 *     };
 *   }, [() => count]); // Pass dependencies as getter functions
 *
 *    useEffect(() => {
 *     // This effect runs when count or doubled changes
 *     console.log('Effect running for count or doubled:', count, doubled);
 *   }, [() => count, () => doubled]);
 *
 *   // Effect that runs only once after mount
 *   useEffect(() => {
 *    console.log('Runs only once after mount');
 *   }, []);
 * </script>
 * ```
 *
 * @see https://svelte.dev/docs/svelte/$effect
 * @param fn The effect function to run. It can optionally return a cleanup function.
 * @param deps An array of getter functions `() => signal` for the dependencies. The effect runs when the value of any signal returned by these getters changes. An empty array `[]` means the effect runs only once after mount.
 */
export function useEffect(fn: () => void | (() => void), deps: Array<Getter<unknown>>) {
	$effect(() => {
		// Read all dependencies by calling the getter functions.
		// This establishes reactivity for the effect based on these signals.
		deps.forEach((dep) => dep());

		// Run the effect function itself untracked, so that reads within fn
		// do not add further dependencies to *this* effect.
		const cleanup = untrack(fn);

		// Return the cleanup function. Svelte will call this before the next
		// effect run or when the component is unmounted.
		return cleanup;
	});
}

/**
 * Creates a polling effect that runs a function at specified intervals
 * @param fn The function to run on each interval
 * @param interval The interval in milliseconds between executions
 * @returns A cleanup function that stops the polling when called
 */
export function usePolling(
	fn: () => void,
	options: { interval: number; immediate?: boolean }
): () => void {
	const { interval, immediate = false } = options;

	if (immediate) {
		fn();
	}

	let timer: NodeJS.Timeout | undefined;

	$effect(() => {
		untrack(() => {
			timer = setInterval(() => {
				fn();
			}, interval);
		});

		return () => {
			clearInterval(timer);
		};
	});

	return () => {
		clearInterval(timer);
	};
}
