<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { Checkbox } from '$lib/components/checkbox/index.js';
	import { ComponentSize } from '$lib/enums/component-size.js';

	const { Story } = defineMeta({
		title: 'Components/Checkbox',
		component: Checkbox,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['checked', 'disabled', 'labelText', 'required', 'indeterminate', 'name', 'size']
			},
			layout: 'centered'
		},
		argTypes: {
			checked: {
				control: { type: 'boolean' },
				description: 'Whether the checkbox is checked'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the checkbox is disabled'
			},
			labelText: {
				control: { type: 'text' },
				description: 'The label text to display next to the checkbox'
			},
			required: {
				control: { type: 'boolean' },
				description: 'Whether the checkbox is required to be checked'
			},
			indeterminate: {
				control: { type: 'boolean' },
				description: 'Whether the checkbox is in an indeterminate state (partially checked)'
			},
			name: {
				control: { type: 'text' },
				description: 'The name attribute for form submission'
			},
			size: {
				control: { type: 'select' },
				options: Object.values(ComponentSize),
				description: 'The size of the checkbox'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		checked: false,
		disabled: false,
		labelText: 'Check me'
	}}
></Story>

<Story
	name="Indeterminate"
	args={{
		checked: false,
		disabled: false,
		indeterminate: true,
		labelText: 'Partially selected'
	}}
></Story>

<Story
	name="Disabled"
	args={{
		checked: false,
		disabled: true,
		labelText: 'Cannot check'
	}}
></Story>

<Story
	name="Disabled Checked"
	args={{
		checked: true,
		disabled: true,
		labelText: 'Cannot uncheck'
	}}
></Story>
