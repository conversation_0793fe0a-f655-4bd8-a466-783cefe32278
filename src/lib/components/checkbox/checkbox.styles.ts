import type { CheckboxVariantConfig } from './checkbox.types.js';
import { ComponentSize } from '$lib/enums/index.js';

const baseStyles = [
	'border-muted',
	'bg-foreground',
	'data-[state=unchecked]:border-border-input',
	'data-[state=unchecked]:bg-background',
	'data-[state=unchecked]:hover:border-dark-40',
	'disabled:data-[state=unchecked]:hover:border-border-input',
	'peer',
	'inline-flex',
	'items-center',
	'justify-center',
	'rounded-md',
	'border',
	'transition-all',
	'duration-150',
	'ease-in-out',
	'active:scale-[0.98]',
	'disabled:cursor-not-allowed',
	'disabled:opacity-50'
].join(' ');

const sizeStyles = {
	[ComponentSize.SM]: 'size-[20px]',
	[ComponentSize.MD]: 'size-[25px]',
	[ComponentSize.LG]: 'size-[30px]'
};

const iconSizeStyles = {
	[ComponentSize.SM]: 'size-[13px]',
	[ComponentSize.MD]: 'size-[15px]',
	[ComponentSize.LG]: 'size-[18px]'
};

export function checkboxVariants(config: CheckboxVariantConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	const classes = [baseStyles, sizeStyles[size], additionalClasses].filter(Boolean).join(' ');

	return classes;
}

export function checkboxIconVariants(config: CheckboxVariantConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	const classes = [iconSizeStyles[size], additionalClasses].filter(Boolean).join(' ');

	return classes;
}
