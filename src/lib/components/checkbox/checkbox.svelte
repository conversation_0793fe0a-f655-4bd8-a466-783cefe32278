<!--
@component
Checkbox - A checkbox component built on top of Bits UI Checkbox.

Provides binary choice input with indeterminate state support and integrated label.

@see {@link https://www.bits-ui.com/docs/components/checkbox | Bits UI Checkbox Documentation}

@example
```svelte
<script lang="ts">
  import { Checkbox } from '@tissue-dynamics/td-ui';

  let isChecked = $state(false);
  let isIndeterminate = $state(false);
</script>

<Checkbox
  bind:checked={isChecked}
  labelText="Accept terms and conditions"
  required
/>

<Checkbox
  bind:checked={isChecked}
  bind:indeterminate={isIndeterminate}
  labelText="Select all items"
/>
```
-->
<script lang="ts">
	import { Checkbox, Label, useId } from 'bits-ui';
	import Check from 'phosphor-svelte/lib/Check';
	import Minus from 'phosphor-svelte/lib/Minus';
	import type { CheckboxProps } from './checkbox.types.js';
	import { ComponentSize } from '$lib/enums/index.js';
	import { checkboxVariants, checkboxIconVariants } from './checkbox.styles.js';
	let {
		id = useId(),
		checked = $bindable(false),
		ref = $bindable(null),
		labelRef = $bindable(null),
		labelText,
		size = ComponentSize.MD,
		class: className = '',
		...restProps
	}: CheckboxProps = $props();

	const checkboxClass = $derived(
		checkboxVariants({
			size,
			class: className
		})
	);

	const iconClass = $derived(
		checkboxIconVariants({
			size
		})
	);
</script>

<div class="flex items-center space-x-3 has-[:disabled]:cursor-not-allowed">
	<Checkbox.Root {id} bind:checked bind:ref {...restProps} class={checkboxClass}>
		{#snippet children({ checked, indeterminate })}
			<div class="text-background inline-flex items-center justify-center">
				{#if indeterminate}
					<Minus class={iconClass} weight="bold" />
				{:else if checked}
					<Check class={iconClass} weight="bold" />
				{/if}
			</div>
		{/snippet}
	</Checkbox.Root>
	<Label.Root
		for={id}
		bind:ref={labelRef}
		class="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
	>
		{labelText}
	</Label.Root>
</div>
