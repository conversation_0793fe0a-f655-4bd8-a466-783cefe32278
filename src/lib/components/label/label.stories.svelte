<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { Label } from '$lib/components/label/index.js';

	const { Story } = defineMeta({
		title: 'Components/Label',
		component: Label,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['for', 'class']
			},
			layout: 'centered'
		},
		argTypes: {
			for: {
				control: { type: 'text' },
				description: 'The id of the input element to label'
			},
			class: {
				control: { type: 'text' },
				description: 'Custom CSS classes to apply to the label'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		for: 'input-id',
		class:
			'text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
	}}
>
	Label text
</Story>
