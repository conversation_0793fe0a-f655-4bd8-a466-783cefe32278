<!--
@component
Label - A semantic label component built on top of Bits UI Label.

Provides proper labeling for form controls with accessibility support.

@see {@link https://www.bits-ui.com/docs/components/label | Bits UI Label Documentation}

@example
```svelte
<script lang="ts">
  import { Label } from '@tissue-dynamics/td-ui';
</script>

<Label for="email-input" class="text-sm font-medium">
  Email Address
</Label>
<input id="email-input" type="email" />

<Label for="password-input">
  Password
</Label>
<input id="password-input" type="password" />
```
-->
<script lang="ts">
	import { Label } from 'bits-ui';
	import type { LabelProps } from './label.types.js';

	let { ...restProps }: LabelProps = $props();
</script>

<Label.Root {...restProps} />
