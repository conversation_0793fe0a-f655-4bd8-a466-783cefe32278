import type { SwitchVariantConfig } from './switch.types.js';
import { ComponentSize } from '$lib/enums/index.js';

const baseRootStyles = [
	// Focus & interaction
	'focus-visible:ring-foreground',
	'focus-visible:ring-offset-background',
	'focus-visible:ring-2',
	'focus-visible:ring-offset-2',
	'focus-visible:outline-hidden',

	// States
	'data-[state=checked]:bg-foreground',
	'data-[state=unchecked]:bg-dark-10',
	'data-[state=unchecked]:shadow-mini-inset',

	// Dark mode
	'dark:data-[state=checked]:bg-foreground',

	// Layout & interaction
	'peer',
	'inline-flex',
	'shrink-0',
	'cursor-pointer',
	'items-center',
	'rounded-full',
	'transition-colors',

	// Disabled state
	'disabled:cursor-not-allowed',
	'disabled:opacity-50'
].join(' ');

const baseThumbStyles = [
	// Background & appearance
	'bg-background',
	'data-[state=unchecked]:shadow-mini',

	// Dark mode
	'dark:border-background/15',
	'dark:bg-foreground',
	'dark:shadow-popover',
	'dark:border',
	'dark:data-[state=unchecked]:border',

	// Layout & interaction
	'pointer-events-none',
	'block',
	'shrink-0',
	'rounded-full',
	'transition-transform',
	'data-[state=unchecked]:translate-x-0'
].join(' ');

const rootSizeStyles = {
	[ComponentSize.SM]: 'h-[18px] min-h-[18px] w-[30px] px-[1.5px]',
	[ComponentSize.MD]: 'h-[24px] min-h-[24px] w-[40px] px-[2px]',
	[ComponentSize.LG]: 'h-[36px] min-h-[36px] w-[60px] px-[3px]'
};

const thumbSizeStyles = {
	[ComponentSize.SM]: 'size-[15px] data-[state=checked]:translate-x-3',
	[ComponentSize.MD]: 'size-[20px] data-[state=checked]:translate-x-4',
	[ComponentSize.LG]: 'size-[30px] data-[state=checked]:translate-x-6'
};

export function switchRootVariants(config: SwitchVariantConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	const classes = [baseRootStyles, rootSizeStyles[size], additionalClasses]
		.filter(Boolean)
		.join(' ');

	return classes;
}

export function switchThumbVariants(config: SwitchVariantConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	const classes = [baseThumbStyles, thumbSizeStyles[size], additionalClasses]
		.filter(Boolean)
		.join(' ');

	return classes;
}
