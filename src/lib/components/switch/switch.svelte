<!--
@component
Switch - A toggle switch component built on top of Bits UI Switch.

Provides binary toggle control with multiple sizes and integrated label.

@see {@link https://www.bits-ui.com/docs/components/switch | Bits UI Switch Documentation}

@example
```svelte
<script lang="ts">
  import { Switch, ComponentSize } from '@tissue-dynamics/td-ui';

  let isEnabled = $state(false);
</script>

<Switch
  bind:checked={isEnabled}
  labelText="Enable notifications"
  size={ComponentSize.MD}
/>

<Switch
  bind:checked={isEnabled}
  labelText="Email notifications"
  disabled={false}
/>
```
-->
<script lang="ts">
	import { Switch, Label, useId } from 'bits-ui';
	import type { SwitchProps } from './switch.types.js';
	import { ComponentSize } from '$lib/enums/index.js';
	import { switchRootVariants, switchThumbVariants } from './switch.styles.js';

	let {
		id = useId(),
		checked = $bindable(false),
		ref = $bindable(null),
		size = ComponentSize.MD,
		class: className = '',
		labelText,
		...restProps
	}: SwitchProps = $props();

	const rootClass = $derived(
		switchRootVariants({
			size,
			class: className
		})
	);

	const thumbClass = $derived(
		switchThumbVariants({
			size
		})
	);

	const labelClass =
		'text-sm font-medium peer-disabled:opacity-50 peer-disabled:cursor-not-allowed';
</script>

<div class="flex items-center space-x-3">
	<Switch.Root bind:checked bind:ref {id} {...restProps} class={rootClass}>
		<Switch.Thumb class={thumbClass} />
	</Switch.Root>
	<Label.Root for={id} class={labelClass}>{labelText}</Label.Root>
</div>
