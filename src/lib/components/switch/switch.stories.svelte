<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { Switch } from '$lib/components/switch/index.js';
	import { ComponentSize } from '$lib/enums/index.js';

	const { Story } = defineMeta({
		title: 'Components/Switch',
		component: Switch,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['checked', 'disabled', 'labelText', 'required', 'size']
			},
			layout: 'centered'
		},
		argTypes: {
			checked: {
				control: { type: 'boolean' },
				description: 'Whether the switch is checked'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the switch is disabled'
			},
			labelText: {
				control: { type: 'text' },
				description: 'The label text to display next to the switch'
			},
			required: {
				control: { type: 'boolean' },
				description: 'Whether the switch is required to be checked'
			},
			size: {
				control: { type: 'select' },
				options: Object.values(ComponentSize),
				description: 'The size of the switch'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		checked: false,
		disabled: false,
		labelText: 'Toggle me',
		size: ComponentSize.MD
	}}
></Story>

<Story
	name="Checked"
	args={{
		checked: true,
		disabled: false,
		labelText: 'Toggle me',
		size: ComponentSize.MD
	}}
></Story>

<Story
	name="Disabled"
	args={{
		checked: false,
		disabled: true,
		labelText: 'Cannot toggle',
		size: ComponentSize.MD
	}}
></Story>

<Story
	name="Disabled Checked"
	args={{
		checked: true,
		disabled: true,
		labelText: 'Cannot toggle',
		size: ComponentSize.MD
	}}
></Story>
