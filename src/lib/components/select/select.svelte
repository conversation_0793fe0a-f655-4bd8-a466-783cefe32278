<!--
@component
Select - A customizable select dropdown component built on top of Bits UI Select.

Provides users with a selectable list of options with enhanced features like keyboard navigation,
custom positioning, and accessibility support. Supports both single and multiple selection modes.

@see {@link https://www.bits-ui.com/docs/components/select | Bits UI Select Documentation}

@example
```svelte
<script lang="ts">
  import { Select } from '@tissue-dynamics/td-ui';

  const themes = [
    { value: 'light-monochrome', label: 'Light Monochrome' },
    { value: 'dark-green', label: 'Dark Green' },
    { value: 'svelte-orange', label: 'Svelte Orange' }
  ];

  let value = $state<string>('');
</script>

<Select
  items={themes}
  bind:value
  placeholder="Select a theme"
/>
```
-->
<script lang="ts">
	import { Select } from 'bits-ui';
	import type { SelectProps } from './select.types.js';
	import { List, Check, CaretUpDown, CaretDoubleUp, CaretDoubleDown } from 'phosphor-svelte';
	import { Placement } from '$lib/enums/placement.js';
	import { mapOffsetToBitsUI, mapPlacementToBitsUI } from '$lib/utils/placement.js';

	let {
		value = $bindable(),
		items,
		contentProps,
		placeholder = 'Select an option',
		placement = Placement.BOTTOM,
		offset = { x: 0, y: 0 },
		triggerProps,
		portalProps,
		itemProps,
		viewportProps,
		...restProps
	}: SelectProps = $props();

	const _placement = $derived(mapPlacementToBitsUI(placement));
	const _offset = $derived(mapOffsetToBitsUI(offset.x, offset.y));

	const selectedLabel = $derived(() => {
		if (!value) return placeholder;

		if (Array.isArray(value)) {
			if (value.length === 0) return placeholder;
			if (value.length === 1) {
				const firstValue = value[0];
				if (firstValue) {
					return items.find((item) => item.value === firstValue)?.label || placeholder;
				}
			}
			return `${value.length} items selected`;
		} else {
			return items.find((item) => item.value === value)?.label || placeholder;
		}
	});

	const displayText = $derived(selectedLabel());
</script>

<!--
TypeScript Discriminated Unions + destructing (required for "bindable") do not
get along, so we shut typescript up by casting `value` to `never`, however,
from the perspective of the consumer of this component, it will be typed appropriately.
-->
<Select.Root bind:value={value as never} {...restProps}>
	<Select.Trigger
		{...triggerProps}
		class="h-input rounded-9px border-border-input bg-background data-placeholder:text-foreground-alt/50 inline-flex w-[296px] touch-none items-center border px-[11px] text-sm transition-colors select-none"
	>
		<List class="text-muted-foreground mr-[9px] size-6" />

		{displayText}
		<CaretUpDown class="text-muted-foreground ml-auto size-6" />
	</Select.Trigger>
	<Select.Portal {...portalProps}>
		<Select.Content
			{...contentProps}
			{..._placement}
			{..._offset}
			class="focus-override border-muted bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 
            shadow-popover z-50  max-h-96 w-[var(--bits-select-anchor-width)] min-w-[var(--bits-select-anchor-width)] rounded-xl border px-1 py-3 outline-hidden select-none data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1"
		>
			<Select.ScrollUpButton class="flex w-full items-center justify-center">
				<CaretDoubleUp class="size-3" />
			</Select.ScrollUpButton>
			<Select.Viewport class="p-1" {...viewportProps}>
				{#each items as { value, label, disabled } (value)}
					<Select.Item
						{value}
						{label}
						{disabled}
						{...itemProps}
						class="rounded-button data-highlighted:bg-muted flex h-10 w-full items-center py-3 pr-1.5 pl-5 text-sm capitalize outline-hidden select-none data-disabled:opacity-50"
					>
						{#snippet children({ selected })}
							{label}

							{#if selected}
								<div class="ml-auto">
									<Check aria-label="check" />
								</div>
							{/if}
						{/snippet}
					</Select.Item>
				{/each}
			</Select.Viewport>
			<Select.ScrollDownButton class="flex w-full items-center justify-center">
				<CaretDoubleDown class="size-3" />
			</Select.ScrollDownButton>
		</Select.Content>
	</Select.Portal>
</Select.Root>
