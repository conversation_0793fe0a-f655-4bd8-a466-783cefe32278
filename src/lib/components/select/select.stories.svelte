<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { Select } from '$lib/components/select/index.js';

	const { Story } = defineMeta({
		title: 'Components/Select',
		component: Select,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['items', 'placeholder', 'disabled', 'open']
			},
			layout: 'centered'
		},
		argTypes: {
			items: {
				control: { type: 'object' },
				description: 'The items to display in the select'
			},
			placeholder: {
				control: { type: 'text' },
				description: 'The placeholder text to display when no value is selected'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the select is disabled'
			},

			open: {
				control: { type: 'boolean' },
				description: 'Whether the select is open (controlled)'
			}
		}
	});

	const themes = [
		{ value: 'light-monochrome', label: 'Light Monochrome' },
		{ value: 'dark-green', label: 'Dark Green' },
		{ value: 'svelte-orange', label: 'Svelte Orange' },
		{ value: 'punk-pink', label: 'Punk Pink' },
		{ value: 'ocean-blue', label: 'Ocean Blue', disabled: true },
		{ value: 'sunset-orange', label: 'Sunset Orange' },
		{ value: 'forest-green', label: 'Forest Green' },
		{ value: 'lavender-purple', label: 'Lavender Purple' },
		{ value: 'mustard-yellow', label: 'Mustard Yellow' },
		{ value: 'slate-gray', label: 'Slate Gray' }
	];

	const countries = [
		{ value: 'us', label: 'United States' },
		{ value: 'ca', label: 'Canada' },
		{ value: 'uk', label: 'United Kingdom' },
		{ value: 'de', label: 'Germany' },
		{ value: 'fr', label: 'France' },
		{ value: 'jp', label: 'Japan' },
		{ value: 'au', label: 'Australia', disabled: true },
		{ value: 'br', label: 'Brazil' }
	];
</script>

<Story
	name="Single Selection"
	args={{
		items: themes,
		placeholder: 'Select a theme',
		type: 'single'
	}}
></Story>

<Story
	name="Multiple Selection"
	args={{
		items: themes,
		placeholder: 'Select themes',
		type: 'multiple'
	}}
></Story>

<Story
	name="Disabled"
	args={{
		items: themes,
		placeholder: 'Select a theme',
		type: 'single',
		disabled: true
	}}
></Story>

<Story
	name="Large Dataset"
	args={{
		items: [
			...themes,
			...countries.map((c) => ({
				...c,
				value: `country-${c.value}`,
				label: `Country: ${c.label}`
			})),
			{ value: 'option-1', label: 'Additional Option 1' },
			{ value: 'option-2', label: 'Additional Option 2' },
			{ value: 'option-3', label: 'Additional Option 3' },
			{ value: 'option-4', label: 'Additional Option 4' },
			{ value: 'option-5', label: 'Additional Option 5' }
		],
		placeholder: 'Select from many options',
		type: 'single'
	}}
></Story>
