import type { Placement } from '$lib/enums/placement.js';
import type { Select, WithoutChildren } from 'bits-ui';

export type SelectProps = WithoutChildren<Select.RootProps> & {
	placeholder?: string;
	items: { value: string; label: string; disabled?: boolean }[];
	placement?: Placement;
	offset?: { x: number; y: number };
	triggerProps?: WithoutChildren<Select.TriggerProps>;
	portalProps?: WithoutChildren<Select.PortalProps>;
	itemProps?: WithoutChildren<Select.ItemProps>;
	viewportProps?: WithoutChildren<Select.ViewportProps>;
	contentProps?: Omit<
		WithoutChildren<Select.ContentProps>,
		'side' | 'align' | 'sideOffset' | 'alignOffset'
	>;
};
