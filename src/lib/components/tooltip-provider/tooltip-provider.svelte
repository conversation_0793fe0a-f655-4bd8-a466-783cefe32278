<!--
@component
TooltipProvider - A provider component for tooltips built on top of Bits UI Tooltip.Provider.

Provides context for tooltip components. Must wrap your app or layout to enable tooltips.
Configures global tooltip behavior like delay duration and hoverable content.

@see {@link https://www.bits-ui.com/docs/components/tooltip | Bits UI Tooltip Documentation}

@example
```svelte
<script lang="ts">
  import { TooltipProvider, Tooltip } from '@tissue-dynamics/td-ui';
</script>

<TooltipProvider>
  <div class="app">
    <Tooltip content="This is a tooltip">
      <button>Hover me</button>
    </Tooltip>
  </div>
</TooltipProvider>
```
-->
<script lang="ts">
	import { Tooltip } from 'bits-ui';
	import type { TooltipProviderProps } from './tooltip-provider.types.js';

	let {
		children,
		delayDuration = 200,
		disableHoverableContent = false,
		...restProps
	}: TooltipProviderProps = $props();
</script>

<Tooltip.Provider {delayDuration} {disableHoverableContent} {...restProps}>
	{@render children()}
</Tooltip.Provider>
