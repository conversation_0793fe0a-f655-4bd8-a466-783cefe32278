<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import TooltipProvider from '$lib/components/tooltip-provider/tooltip-provider.svelte';
	import Tooltip from '$lib/components/tooltip/tooltip.svelte';

	const { Story } = defineMeta({
		title: 'Components/TooltipProvider',
		component: TooltipProvider,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['delayDuration', 'disableHoverableContent']
			},
			layout: 'centered'
		},
		argTypes: {
			delayDuration: {
				control: 'number',
				description: 'The delay in milliseconds before the tooltip appears'
			},
			disableHoverableContent: {
				control: 'boolean',
				description: 'Whether to disable hoverable content'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		delayDuration: 200,
		disableHoverableContent: false
        
	}}
>
	<Tooltip content="This is a helpful tooltip">
		<span
			class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
		>
			Hover me
		</span>
	</Tooltip>
</Story>
