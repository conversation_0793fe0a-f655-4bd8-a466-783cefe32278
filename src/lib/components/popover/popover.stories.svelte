<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import Popover from '$lib/components/popover/popover.svelte';
	import { Placement } from '$lib/enums/placement.js';

	const { Story } = defineMeta({
		title: 'Components/Popover',
		component: Popover,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['triggerText', 'title', 'content', 'showClose', 'showArrow', 'open', 'placement']
			},
			layout: 'centered'
		},
		argTypes: {
			triggerText: {
				control: { type: 'text' },
				description: 'Text for the trigger button'
			},
			title: {
				control: { type: 'text' },
				description: 'Optional title for the popover'
			},
			content: {
				control: { type: 'text' },
				description: 'Content text for the popover'
			},
			showClose: {
				control: { type: 'boolean' },
				description: 'Whether to show the close button'
			},
			showArrow: {
				control: { type: 'boolean' },
				description: 'Whether to show the arrow'
			},
			open: {
				control: { type: 'boolean' },
				description: 'Whether the popover is open (controlled)'
			},
			placement: {
				control: { type: 'select' },
				options: Object.values(Placement),
				description: 'The placement of the dropdown menu relative to the trigger'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		triggerText: 'Open Popover',
		title: 'Popover Title',
		content:
			'This is the popover content. You can put any content here including forms, buttons, or other components.',
		showClose: true,
		showArrow: false
	}}
></Story>

<Story
	name="With Arrow"
	args={{
		triggerText: 'Open with Arrow',
		title: 'Popover with Arrow',
		content: 'This popover includes an arrow pointing to the trigger element.',
		showClose: false,
		showArrow: true
	}}
></Story>

<Story
	name="No Close Button"
	args={{
		triggerText: 'Info',
		title: 'Information',
		content: "This popover doesn't have a close button. Click outside to close.",
		showClose: false,
		showArrow: false
	}}
></Story>

<Story
	name="Long Content"
	args={{
		triggerText: 'Read More',
		title: 'Detailed Information',
		content:
			'loren ipsum dolor sit amet, consectetur adipiscing elit. lorem ipsum dolor sit amet, consectetur adipiscing elit. This is a longer content example that demonstrates how the popover handles more text. The popover will automatically adjust its layout to accommodate the content while maintaining good readability and visual hierarchy.loren ipsum dolor sit amet, consectetur adipiscing elit. lorem ipsum dolor sit amet, consectetur adipiscing elit. This is a longer content example that demonstrates how the popover handles more text. The popover will automatically adjust its layout to accommodate the content while maintaining good readability and visual hierarchy.loren ipsum dolor sit amet, consectetur adipiscing elit. lorem ipsum dolor sit amet, consectetur adipiscing elit. This is a longer content example that demonstrates how the popover handles more text. The popover will automatically adjust its layout to accommodate the content while maintaining good readability and visual hierarchy.loren ipsum dolor sit amet, consectetur adipiscing elit. lorem ipsum dolor sit amet, consectetur adipiscing elit. This is a longer content example that demonstrates how the popover handles more text. The popover will automatically adjust its layout to accommodate the content while maintaining good readability and visual hierarchy.loren ipsum dolor sit amet, consectetur adipiscing elit. lorem ipsum dolor sit amet, consectetur adipiscing elit. This is a longer content example that demonstrates how the popover handles more text. The popover will automatically adjust its layout to accommodate the content while maintaining good readability and visual hierarchy.loren ipsum dolor sit amet, consectetur adipiscing elit. lorem ipsum dolor sit amet, consectetur adipiscing elit. This is a longer content example that demonstrates how the popover handles more text. The popover will automatically adjust its layout to accommodate the content while maintaining good readability and visual hierarchy.loren ipsum dolor sit amet, consectetur adipiscing elit. lorem ipsum dolor sit amet, consectetur adipiscing elit. This is a longer content example that demonstrates how the popover handles more text. The popover will automatically adjust its layout to accommodate the content while maintaining good readability and visual hierarchy.',

		showClose: true,
		showArrow: true
	}}
></Story>

<Story
	name="Complex Content"
	args={{
		triggerText: 'Open Complex Popover',
		title: 'Custom Content',
		content: '',
		showClose: true,
		showArrow: false
	}}
>
	<div class="space-y-4">
		<input
			type="text"
			placeholder="Name"
			class="border-border-input bg-background ring-offset-background hover:bg-muted focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium whitespace-nowrap transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50"
		/>
		<input
			type="email"
			placeholder="Email"
			class="border-border-input bg-background ring-offset-background hover:bg-muted focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium whitespace-nowrap transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50"
		/>
		<button
			class="border-border-input bg-background ring-offset-background hover:bg-muted focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium whitespace-nowrap transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50"
		>
			Submit
		</button>
	</div>
</Story>
