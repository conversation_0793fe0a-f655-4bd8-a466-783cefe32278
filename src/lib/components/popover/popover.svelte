<!--
@component
Popover - A styled popover component built on top of Bits UI Popover.

Display supplementary content or information when users interact with specific elements.
Features automatic scrolling for long content with optional close button and arrow.
Supports both simple text content and complex children content.

@see {@link https://www.bits-ui.com/docs/components/popover | Bits UI Popover Documentation}

@example
```svelte
<script lang="ts">
  import { Popover } from '@tissue-dynamics/td-ui';
</script>

<Popover
  triggerText="Open Popover"
  title="Popover Title"
  content="This is the popover content"
/>

<Popover triggerText="Open Complex Popover" title="Custom Content">
  <div class="space-y-4">
    <input type="text" placeholder="Name" />
    <button>Submit</button>
  </div>
</Popover>
```
-->
<script lang="ts">
	import { Popover } from 'bits-ui';
	import type { PopoverProps } from './popover.types.js';
	import { Placement } from '$lib/enums/placement.js';
	import { mapOffsetToBitsUI, mapPlacementToBitsUI } from '$lib/utils/placement.js';
	import { X } from 'phosphor-svelte';

	let {
		open = $bindable(false),
		triggerText,
		title,
		content,
		children,
		placement = Placement.BOTTOM,
		offset = { x: 0, y: 4 },
		showClose = true,
		showArrow = false,
		...restProps
	}: PopoverProps = $props();

	const _placement = $derived(mapPlacementToBitsUI(placement));
	const _offset = $derived(mapOffsetToBitsUI(offset.x, offset.y));
</script>

<Popover.Root bind:open {...restProps}>
	<Popover.Trigger
		class="bg-foreground text-background ring-offset-background hover:bg-foreground/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium whitespace-nowrap transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50"
	>
		{triggerText}
	</Popover.Trigger>
	<Popover.Portal>
		<Popover.Content
			{..._placement}
			{..._offset}
			class="bg-background border-border-card shadow-popover data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-80 rounded-xl border outline-hidden"
		>
			{#if showArrow}
				<Popover.Arrow class="fill-background stroke-border-card stroke-1" />
			{/if}
			<div class="flex max-h-96 flex-col">
				<div class="min-h-0 flex-1 overflow-y-auto p-4">
					{#if children}
						<div class="space-y-2">
							{#if title}
								<h4 class="text-foreground font-medium">{title}</h4>
							{/if}
							{@render children()}
						</div>
					{:else}
						<div class="space-y-2">
							{#if title}
								<h4 class="text-foreground font-medium">{title}</h4>
							{/if}
							{#if content}
								<p class="text-muted-foreground text-sm">{content}</p>
							{/if}
						</div>
					{/if}
				</div>
				{#if showClose}
					<div class="border-border-card flex flex-shrink-0 justify-end border-t p-4 pt-3">
						<Popover.Close
							class="border-border-input bg-background ring-offset-background hover:bg-muted focus-visible:ring-ring inline-flex h-8 items-center justify-center rounded-md border px-3 py-1 text-sm font-medium whitespace-nowrap transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50"
						>
							<X class="mr-2 size-4" />
							Close
						</Popover.Close>
					</div>
				{/if}
			</div>
		</Popover.Content>
	</Popover.Portal>
</Popover.Root>
