import type { Placement } from '$lib/enums/placement.js';
import type { Popover, WithoutChildren } from 'bits-ui';
import type { Snippet } from 'svelte';

export type PopoverProps = WithoutChildren<Popover.RootProps> & {
	triggerText: string;
	title?: string;
	content?: string;
	children?: Snippet;
	placement?: Placement;
	offset?: { x: number; y: number };
	showClose?: boolean;
	showArrow?: boolean;
	triggerProps?: WithoutChildren<Popover.TriggerProps>;
	portalProps?: WithoutChildren<Popover.PortalProps>;
	contentProps?: Omit<
		WithoutChildren<Popover.ContentProps>,
		'side' | 'align' | 'sideOffset' | 'alignOffset'
	>;
	closeProps?: WithoutChildren<Popover.CloseProps>;
	arrowProps?: WithoutChildren<Popover.ArrowProps>;
};
