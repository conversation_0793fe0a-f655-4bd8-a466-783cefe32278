import type { RadioGroupVariantConfig } from './radio-group.types.js';
import { ComponentSize } from '$lib/enums/index.js';

const baseItemStyles = [
	// Background & appearance
	'border-border-input',
	'bg-background',
	'data-[state=checked]:border-foreground',
	'shrink-0',
	'rounded-full',
	'border',
	'transition-all',
	'duration-100',
	'ease-in-out',
	'data-[state=checked]:border-6'
].join(' ');

const baseItemInteractionStyles = ['hover:border-dark-40', 'cursor-default'].join(' ');

const baseItemDisabledStyles = ['cursor-not-allowed'].join(' ');

const itemSizeStyles = {
	[ComponentSize.SM]: 'size-[14px]',
	[ComponentSize.MD]: 'size-[16px]',
	[ComponentSize.LG]: 'size-[18px]'
};

const labelSizeStyles = {
	[ComponentSize.SM]: 'text-xs',
	[ComponentSize.MD]: 'text-sm',
	[ComponentSize.LG]: 'text-base'
};

const containerSizeStyles = {
	[ComponentSize.SM]: 'gap-3',
	[ComponentSize.MD]: 'gap-4',
	[ComponentSize.LG]: 'gap-5'
};

export function radioGroupItemVariants(
	config: RadioGroupVariantConfig & { disabled?: boolean } = {}
): string {
	const { size = ComponentSize.MD, disabled = false, class: additionalClasses = '' } = config;

	const classes = [
		baseItemStyles,
		itemSizeStyles[size],
		disabled ? baseItemDisabledStyles : baseItemInteractionStyles,
		additionalClasses
	]
		.filter(Boolean)
		.join(' ');

	return classes;
}

export function radioGroupLabelVariants(config: RadioGroupVariantConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	const classes = [labelSizeStyles[size], 'pl-3', additionalClasses].filter(Boolean).join(' ');

	return classes;
}

export function radioGroupContainerVariants(config: RadioGroupVariantConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	const classes = ['flex', 'flex-col', containerSizeStyles[size], 'font-medium', additionalClasses]
		.filter(Boolean)
		.join(' ');

	return classes;
}
