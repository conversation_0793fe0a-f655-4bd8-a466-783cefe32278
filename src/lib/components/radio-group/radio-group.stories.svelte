<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { RadioGroup } from '$lib/components/radio-group/index.js';
	import { ComponentSize } from '$lib/enums/index.js';

	const { Story } = defineMeta({
		title: 'Components/RadioGroup',
		component: RadioGroup,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['items', 'value', 'disabled', 'size']
			},
			layout: 'centered'
		},
		argTypes: {
			items: {
				control: { type: 'object' },
				description: 'The items to display in the radio group'
			},
			value: {
				control: { type: 'text' },
				description: 'The value of the selected item'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the radio group is disabled'
			},
			size: {
				control: { type: 'select' },
				options: Object.values(ComponentSize),
				description: 'The size of the radio group'
			}
		}
	});

	const items = [
		{ value: '1', label: 'Option 1' },
		{ value: '2', label: 'Option 2' },
		{ value: '3', label: 'Option 3' }
	];

	const itemsWithDisabled = [
		{ value: '1', label: 'Option 1' },
		{ value: '2', label: 'Option 2', disabled: true },
		{ value: '3', label: 'Option 3' }
	];
</script>

<Story
	name="Default"
	args={{
		items: items,
		value: '1',
		disabled: false,
		size: ComponentSize.MD
	}}
></Story>

<Story
	name="Small"
	args={{
		items: items,
		value: '1',
		disabled: false,
		size: ComponentSize.SM
	}}
></Story>

<Story
	name="Large"
	args={{
		items: items,
		value: '1',
		disabled: false,
		size: ComponentSize.LG
	}}
></Story>

<Story
	name="Disabled"
	args={{
		items: items,
		value: '1',
		disabled: true,
		size: ComponentSize.MD
	}}
></Story>

<Story
	name="With Disabled Items"
	args={{
		items: itemsWithDisabled,
		value: '1',
		disabled: false,
		size: ComponentSize.MD
	}}
></Story>
