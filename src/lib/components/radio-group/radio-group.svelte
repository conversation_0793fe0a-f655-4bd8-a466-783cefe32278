<!--
@component
RadioGroup - A radio button group component built on top of Bits UI RadioGroup.

Provides mutually exclusive selection from a list of options with support for disabled states
and form integration.

@see {@link https://www.bits-ui.com/docs/components/radio-group | Bits UI RadioGroup Documentation}

@example
```svelte
<script lang="ts">
  import { RadioGroup, ComponentSize } from '@tissue-dynamics/td-ui';

  let selectedTheme = $state('light');
  const themes = [
    { value: 'light', label: 'Light Theme' },
    { value: 'dark', label: 'Dark Theme' },
    { value: 'auto', label: 'Auto (System)' }
  ];
</script>

<RadioGroup
  bind:value={selectedTheme}
  items={themes}
  name="theme-selection"
  size={ComponentSize.MD}
/>
```

@example With Disabled Items
```svelte
<RadioGroup
  bind:value={selectedPriority}
  items={[
    { value: 'low', label: 'Low Priority' },
    { value: 'medium', label: 'Medium Priority' },
    { value: 'high', label: 'High Priority', disabled: true }
  ]}
  required
/>
```
-->

<script lang="ts">
	import { RadioGroup, Label, useId } from 'bits-ui';
	import type { RadioGroupProps } from './radio-group.types.js';
	import { ComponentSize } from '$lib/enums/index.js';
	import {
		radioGroupContainerVariants,
		radioGroupItemVariants,
		radioGroupLabelVariants
	} from './radio-group.styles.js';

	let {
		value = $bindable(''),
		ref = $bindable(null),
		items,
		disabled = false,
		size = ComponentSize.MD,
		class: className = '',
		...restProps
	}: RadioGroupProps = $props();

	const containerClass = $derived(
		radioGroupContainerVariants({
			size,
			class: className
		})
	);
</script>

<RadioGroup.Root bind:value bind:ref {disabled} {...restProps} class={containerClass}>
	{#each items as item}
		{@const id = useId()}
		{@const isDisabled = disabled || item.disabled}
		{@const itemClass = radioGroupItemVariants({ size, disabled: isDisabled })}
		{@const labelClass = radioGroupLabelVariants({ size })}
		<div
			class="text-foreground group flex items-center transition-all select-none {isDisabled
				? 'opacity-50'
				: ''}"
		>
			<RadioGroup.Item {id} value={item.value} disabled={isDisabled} class={itemClass}
			></RadioGroup.Item>
			<Label.Root for={id} class="{labelClass} {isDisabled ? 'cursor-not-allowed' : ''}"
				>{item.label}</Label.Root
			>
		</div>
	{/each}
</RadioGroup.Root>
