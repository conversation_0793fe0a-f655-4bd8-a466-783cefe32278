import type { RadioGroup, WithoutChildrenOrChild } from 'bits-ui';
import type { ComponentSize } from '$lib/enums/index.js';

export type RadioGroupItem = {
	value: string;
	label: string;
	disabled?: boolean;
};

export type RadioGroupVariantConfig = {
	size?: ComponentSize;
	class?: string;
};

export type RadioGroupProps = WithoutChildrenOrChild<RadioGroup.RootProps> & {
	disabled?: boolean;
	items: RadioGroupItem[];
	size?: ComponentSize;
	class?: string;
};
