<!--
@component
Accordion - A customizable accordion component built on top of Bits UI Accordion.

Organizes content into collapsible sections, allowing users to focus on one or more sections at a time.
Supports both single and multiple selection modes with smooth animations and full accessibility support.

@see {@link https://www.bits-ui.com/docs/components/accordion | Bits UI Accordion Documentation}

@example
```svelte
<script lang="ts">
  import { Accordion, AccordionSize, SelectionMode, Orientation, type AccordionItem } from '@tissue-dynamics/td-ui';

  const faqItems: AccordionItem[] = [
    {
      value: 'item-1',
      title: 'What is the meaning of life?',
      content: 'To become a better person, to help others, and to leave the world a better place than you found it.'
    },
    {
      value: 'item-2',
      title: 'How do I become a better person?',
      content: 'Read books, listen to podcasts, and surround yourself with people who inspire you.'
    },
    {
      value: 'item-3',
      title: 'What is the best way to help others?',
      content: 'Give them your time, attention, and love.'
    }
  ];

  let value = $state<string>('');
</script>

<Accordion
  type={SelectionMode.SINGLE}
  items={faqItems}
  size={AccordionSize.MD}
  bind:value
/>
```
-->
<script lang="ts">
	import { Accordion } from 'bits-ui';
	import type { AccordionItem, AccordionProps } from './accordion.types.js';
	import { ComponentSize } from '$lib/enums/component-size.js';
	import { Orientation } from '$lib/enums/orientation.js';
	import {
		accordionItemVariants,
		accordionTriggerVariants,
		accordionIconVariants,
		accordionIconInnerVariants,
		accordionContentVariants,
		accordionContentPaddingVariants,
		accordionWidthVariants,
		accordionHeightVariants
	} from './accordion.styles.js';
	import { CaretDown } from 'phosphor-svelte';

	let {
		value = $bindable(),
		items,
		size = ComponentSize.MD,
		orientation = Orientation.VERTICAL,
		triggerProps,
		contentProps,
		itemProps,
		headerProps,
		...restProps
	}: AccordionProps = $props();

	const itemClass = $derived(accordionItemVariants({ orientation }));
	const triggerClass = $derived(accordionTriggerVariants({ size, orientation }));
	const iconClass = $derived(accordionIconVariants({ size }));
	const iconInnerClass = $derived(accordionIconInnerVariants({ size }));
	const contentClass = $derived(accordionContentVariants({ size, orientation }));
	const contentPaddingClass = $derived(accordionContentPaddingVariants({ size }));
	const computedWidth = $derived(accordionWidthVariants({ size }));
	const computedHeight = $derived(accordionHeightVariants({ size }));

	const rootClass = $derived(
		orientation === Orientation.HORIZONTAL
			? `flex gap-2 ${computedHeight} ${computedWidth}`
			: computedWidth
	);

	function handleClick(item: AccordionItem) {
		if (orientation === Orientation.HORIZONTAL) {
			value = item.value;
		}
	}
</script>

<!--
TypeScript Discriminated Unions + destructing (required for "bindable") do not
get along, so we shut typescript up by casting `value` to `never`, however,
from the perspective of the consumer of this component, it will be typed appropriately.
-->
<Accordion.Root bind:value={value as never} {orientation} class={rootClass} {...restProps}>
	{#each items as item: AccordionItem (item.value)}
		<Accordion.Item
			value={item.value}
			disabled={item.disabled}
			onclick={() => handleClick(item)}
			{...itemProps}
			class={itemClass}
		>
			{#if orientation === Orientation.HORIZONTAL}
				{#if item.image}
					<img src={item.image} alt={item.title} class="h-[400px] w-full object-cover" />
				{/if}
				<div
					class="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4"
				>
					<div
						class="transition-all duration-300 ease-out group-data-[state=closed]:translate-y-2 group-data-[state=open]:translate-y-0"
					>
						<Accordion.Header {...headerProps}>
							<Accordion.Trigger {...triggerProps} class={triggerClass}>
								{item.title}
							</Accordion.Trigger>
						</Accordion.Header>
						<Accordion.Content {...contentProps} class={contentClass}>
							<div class={contentPaddingClass}>
								{item.content}
							</div>
						</Accordion.Content>
					</div>
				</div>
			{:else}
				<Accordion.Header {...headerProps}>
					<Accordion.Trigger {...triggerProps} class={triggerClass}>
						<span class="w-full text-left">
							{item.title}
						</span>
						<span class={iconClass}>
							<CaretDown class="{iconInnerClass} transition-transform duration-200" />
						</span>
					</Accordion.Trigger>
				</Accordion.Header>
				<Accordion.Content {...contentProps} class={contentClass}>
					<div class={contentPaddingClass}>
						{item.content}
					</div>
				</Accordion.Content>
			{/if}
		</Accordion.Item>
	{/each}
</Accordion.Root>
