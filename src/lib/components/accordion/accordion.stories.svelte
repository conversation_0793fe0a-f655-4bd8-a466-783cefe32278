<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { Accordion, type AccordionItem } from '$lib/components/accordion/index.js';
	import { Orientation } from '$lib/enums/orientation.js';
	import { ComponentSize } from '$lib/enums/component-size.js';
	import { SelectionMode } from '$lib/enums/selection-mode.js';

	const { Story } = defineMeta({
		title: 'Components/Accordion',
		component: Accordion,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['items', 'type', 'size', 'disabled', 'orientation']
			},
			layout: 'centered'
		},
		argTypes: {
			items: {
				control: { type: 'object' },
				description: 'The items to display in the accordion'
			},
			type: {
				control: { type: 'select' },
				options: Object.values(SelectionMode),
				description: 'Selection mode - single allows one item open, multiple allows multiple'
			},
			size: {
				control: { type: 'select' },
				options: Object.values(ComponentSize),
				description: 'Accordion size (sm, md, lg)'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the entire accordion is disabled'
			},
			orientation: {
				control: { type: 'select' },
				options: Object.values(Orientation),
				description: 'The orientation of the accordion'
			}
		}
	});

	const faqItems: AccordionItem[] = [
		{
			value: 'item-1',
			title: 'What is the meaning of life?',
			content:
				'To become a better person, to help others, and to leave the world a better place than you found it.'
		},
		{
			value: 'item-2',
			title: 'How do I become a better person?',
			content: 'Read books, listen to podcasts, and surround yourself with people who inspire you.'
		},
		{
			value: 'item-3',
			title: 'What is the best way to help others?',
			content: 'Give them your time, attention, and love.'
		}
	];

	const featureItems: AccordionItem[] = [
		{
			value: 'features',
			title: 'Features',
			content:
				'Our product includes advanced analytics, real-time collaboration, and seamless integrations with your favorite tools.'
		},
		{
			value: 'pricing',
			title: 'Pricing',
			content:
				'We offer flexible pricing plans starting from $9/month for individuals and custom enterprise solutions.'
		},
		{
			value: 'support',
			title: 'Support',
			content:
				'24/7 customer support via chat, email, and phone. Plus comprehensive documentation and video tutorials.',
			disabled: true
		},
		{
			value: 'security',
			title: 'Security',
			content:
				'Enterprise-grade security with end-to-end encryption, SOC 2 compliance, and regular security audits.'
		}
	];

	const horizontalItems: AccordionItem[] = [
		{
			value: 'mountain',
			title: 'Mountain Range',
			content: 'Majestic mountain ranges with snow-capped peaks and lush valleys.',
			image:
				'https://images.unsplash.com/photo-1586589058841-f1264894a260?q=80&w=600&auto=format&fit=crop&ixlib=rb-4.0.3'
		},
		{
			value: 'ocean',
			title: 'Ocean Views',
			content: 'Serene ocean scenes with crashing waves, beautiful sunsets, and sandy beaches.',
			image:
				'https://images.unsplash.com/photo-1650300874827-7d39bc9276ea?q=80&w=600&auto=format&fit=crop&ixlib=rb-4.0.3'
		},
		{
			value: 'forest',
			title: 'Forest Retreats',
			content: 'Dense forests with towering trees, abundant wildlife, and peaceful streams.',
			image:
				'https://images.unsplash.com/photo-1693297490324-37ee6301f6c8?q=80&w=600&auto=format&fit=crop&ixlib=rb-4.0.3'
		}
	];
</script>

<Story
	name="Single Selection"
	args={{
		items: faqItems,
		type: SelectionMode.SINGLE,
		size: ComponentSize.MD
	}}
></Story>

<Story
	name="Multiple Selection"
	args={{
		items: faqItems,
		type: SelectionMode.MULTIPLE,
		size: ComponentSize.MD
	}}
></Story>

<Story
	name="With Disabled Items"
	args={{
		items: featureItems,
		type: SelectionMode.SINGLE,
		size: ComponentSize.MD
	}}
></Story>

<Story
	name="Disabled Accordion"
	args={{
		items: faqItems,
		type: SelectionMode.SINGLE,
		size: ComponentSize.MD,
		disabled: true
	}}
></Story>

<Story
	name="Horizontal Orientation"
	args={{
		items: horizontalItems,
		size: ComponentSize.LG,
		orientation: 'horizontal'
	}}
></Story>
