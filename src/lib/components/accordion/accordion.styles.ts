import type { AccordionVariantConfig } from './accordion.types.js';
import { Orientation } from '$lib/enums/orientation.js';
import { ComponentSize } from '$lib/enums/component-size.js';

const baseItemStyles = ['border-dark-10', 'group', 'border-b', 'px-1.5'].join(' ');

const baseTriggerStyles = [
	'flex',
	'w-full',
	'flex-1',
	'items-center',
	'justify-between',
	'font-medium',
	'transition-all',
	'select-none',
	'[&[data-state=open]>span>svg]:rotate-180',
	'disabled:cursor-not-allowed',
	'disabled:opacity-50'
].join(' ');

const baseIconStyles = [
	'hover:bg-dark-10',
	'inline-flex',
	'items-center',
	'justify-center',
	'rounded-[7px]',
	'bg-transparent'
].join(' ');

const baseContentStyles = [
	'data-[state=closed]:animate-accordion-up',
	'data-[state=open]:animate-accordion-down',
	'overflow-hidden',
	'tracking-[-0.01em]'
].join(' ');

const triggerSizeStyles = {
	[ComponentSize.SM]: 'py-3 text-sm',
	[ComponentSize.MD]: 'py-4 text-[15px]',
	[ComponentSize.LG]: 'py-5 text-base'
};

const iconSizeStyles = {
	[ComponentSize.SM]: 'size-6',
	[ComponentSize.MD]: 'size-7',
	[ComponentSize.LG]: 'size-8'
};

const iconInnerSizeStyles = {
	[ComponentSize.SM]: 'size-[14px]',
	[ComponentSize.MD]: 'size-[18px]',
	[ComponentSize.LG]: 'size-[22px]'
};

const contentSizeStyles = {
	[ComponentSize.SM]: 'text-[15px]',
	[ComponentSize.MD]: 'text-sm',
	[ComponentSize.LG]: 'text-base'
};

const contentPaddingSizeStyles = {
	[ComponentSize.SM]: 'pb-[6px]',
	[ComponentSize.MD]: 'pb-[25px]',
	[ComponentSize.LG]: 'pb-[35px]'
};

const widthSizeStyles = {
	[ComponentSize.SM]: 'w-[300px]',
	[ComponentSize.MD]: 'w-[350px]',
	[ComponentSize.LG]: 'w-[450px]'
};

const heightSizeStyles = {
	[ComponentSize.SM]: 'h-[200px]',
	[ComponentSize.MD]: 'h-[300px]',
	[ComponentSize.LG]: 'h-[400px]'
};

export function accordionItemVariants(config: AccordionVariantConfig = {}): string {
	const { orientation = Orientation.VERTICAL, class: additionalClasses = '' } = config;

	const orientationStyles =
		orientation === Orientation.HORIZONTAL
			? 'relative cursor-pointer overflow-hidden rounded-lg transition-all duration-500 ease-out data-[state=closed]:w-[20%] data-[state=open]:w-[100%] md:data-[state=closed]:w-[10%] border-0 px-0 [&:has(:focus-visible)]:ring-2 ring-primary/70'
			: baseItemStyles;

	const classes = [orientationStyles, additionalClasses].filter(Boolean).join(' ');

	return classes;
}

export function accordionTriggerVariants(config: AccordionVariantConfig = {}): string {
	const {
		size = ComponentSize.MD,
		orientation = Orientation.VERTICAL,
		class: additionalClasses = ''
	} = config;

	const orientationStyles =
		orientation === Orientation.HORIZONTAL
			? 'focus-override text-left font-bold text-white transition-all duration-300 ease-out focus-visible:!outline-none data-[state=open]:mb-2 data-[state=closed]:text-sm data-[state=open]:text-base data-[state=closed]:opacity-0 data-[state=open]:opacity-100 md:data-[state=open]:text-xl'
			: baseTriggerStyles;

	const classes = [orientationStyles, triggerSizeStyles[size], additionalClasses]
		.filter(Boolean)
		.join(' ');

	return classes;
}

export function accordionIconVariants(config: AccordionVariantConfig = {}): string {
	const { size = ComponentSize.MD, class: additionalClasses = '' } = config;

	const classes = [baseIconStyles, iconSizeStyles[size], additionalClasses]
		.filter(Boolean)
		.join(' ');

	return classes;
}

export function accordionIconInnerVariants(config: AccordionVariantConfig = {}): string {
	const { size = ComponentSize.MD } = config;

	return iconInnerSizeStyles[size];
}

export function accordionContentVariants(config: AccordionVariantConfig = {}): string {
	const {
		size = ComponentSize.MD,
		orientation = Orientation.VERTICAL,
		class: additionalClasses = ''
	} = config;

	const orientationStyles =
		orientation === Orientation.HORIZONTAL
			? 'max-h-0 overflow-hidden text-white/90 transition-all duration-700 ease-out data-[state=open]:max-h-[100px] data-[state=open]:text-xs data-[state=closed]:opacity-0 data-[state=open]:opacity-100 md:data-[state=open]:text-base'
			: baseContentStyles;

	const classes = [orientationStyles, contentSizeStyles[size], additionalClasses]
		.filter(Boolean)
		.join(' ');

	return classes;
}

export function accordionContentPaddingVariants(config: AccordionVariantConfig = {}): string {
	const { size = ComponentSize.MD } = config;

	return contentPaddingSizeStyles[size];
}

export function accordionWidthVariants(config: AccordionVariantConfig = {}): string {
	const { size = ComponentSize.MD } = config;

	return widthSizeStyles[size];
}

export function accordionHeightVariants(config: AccordionVariantConfig = {}): string {
	const { size = ComponentSize.MD } = config;

	return heightSizeStyles[size];
}
