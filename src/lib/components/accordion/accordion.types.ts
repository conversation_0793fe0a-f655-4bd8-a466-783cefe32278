import type { Accordion, WithoutChildren } from 'bits-ui';
import type { Orientation } from '$lib/enums/orientation.js';
import type { ComponentSize } from '$lib/enums/component-size.js';

export type AccordionItem = {
	value: string;
	title: string;
	content: string;
	disabled?: boolean;
	image?: string;
};

export type AccordionVariantConfig = {
	size?: ComponentSize;
	orientation?: Orientation;
	class?: string;
};

export type AccordionProps = WithoutChildren<Accordion.RootProps> & {
	items: AccordionItem[];
	size?: ComponentSize;
	triggerProps?: WithoutChildren<Accordion.TriggerProps>;
	contentProps?: WithoutChildren<Accordion.ContentProps>;
	itemProps?: WithoutChildren<Accordion.ItemProps>;
	headerProps?: WithoutChildren<Accordion.HeaderProps>;
};
