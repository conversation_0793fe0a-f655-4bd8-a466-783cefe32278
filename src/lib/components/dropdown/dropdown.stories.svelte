<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { DropdownMenu } from '$lib/components/dropdown/index.js';
	import { Placement } from '$lib/enums/placement.js';

	const { Story } = defineMeta({
		title: 'Components/Dropdown',
		component: DropdownMenu,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['buttonText', 'items', 'placement', 'offset', 'open', 'disabled']
			},
			layout: 'centered'
		},
		argTypes: {
			buttonText: {
				control: { type: 'text' },
				description:
					'The text to display on the dropdown button. If not provided, shows a dots icon.'
			},
			items: {
				control: { type: 'object' },
				description: 'The items to display in the dropdown menu'
			},
			placement: {
				control: { type: 'select' },
				options: Object.values(Placement),
				description: 'The placement of the dropdown menu relative to the trigger'
			},
			offset: {
				control: { type: 'object' },
				description: 'The offset of the dropdown menu from the trigger (x, y coordinates)'
			},
			open: {
				control: { type: 'boolean' },
				description: 'Controls whether the dropdown is open or closed'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the dropdown trigger is disabled'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		buttonText: 'Options',
		items: ['Edit', 'Duplicate', 'Archive', 'Delete'],
		placement: Placement.BOTTOM_START,
		offset: { x: 0, y: 4 },
		open: false,
		disabled: false
	}}
></Story>

<Story
	name="Icon Only"
	args={{
		items: ['Profile', 'Settings', 'Billing', 'Sign out'],
		placement: Placement.BOTTOM_END,
		offset: { x: 0, y: 8 },
		open: false,
		disabled: false
	}}
></Story>

<Story
	name="Open by Default"
	args={{
		buttonText: 'Open Menu',
		items: ['Option 1', 'Option 2', 'Option 3'],
		offset: { x: 0, y: 4 },
		open: true,
		disabled: false
	}}
></Story>

<Story
	name="Disabled"
	args={{
		buttonText: 'Disabled Menu',
		items: ['Item 1', 'Item 2', 'Item 3'],
		placement: Placement.BOTTOM_START,
		offset: { x: 0, y: 4 },
		open: false,
		disabled: true
	}}
></Story>
