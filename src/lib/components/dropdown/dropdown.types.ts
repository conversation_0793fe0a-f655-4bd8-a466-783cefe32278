import type { Placement } from '$lib/enums/placement.js';
import type { DropdownMenu, WithoutChild } from 'bits-ui';

export type DropdownProps = DropdownMenu.RootProps & {
	buttonText?: string;
	items: string[];
	disabled?: boolean;
	placement?: Placement;
	offset?: { x: number; y: number };
	contentProps?: Omit<
		WithoutChild<DropdownMenu.Content>,
		'side' | 'align' | 'sideOffset' | 'alignOffset'
	>;
};
