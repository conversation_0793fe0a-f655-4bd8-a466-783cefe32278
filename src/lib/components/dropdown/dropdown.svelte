<!--
@component
Dropdown - A dropdown menu component built on top of Bits UI DropdownMenu.

Provides a menu of selectable items with customizable trigger button and positioning.

@see {@link https://www.bits-ui.com/docs/components/dropdown-menu | Bits UI DropdownMenu Documentation}

@example
```svelte
<script lang="ts">
  import { DropdownMenu } from '@tissue-dynamics/td-ui';
</script>

<DropdownMenu
  buttonText="Options"
  items={['Edit', 'Duplicate', 'Archive', 'Delete']}
/>

<DropdownMenu
  items={['Profile', 'Settings', 'Sign out']}
  disabled={false}
/>
```

-->
<script lang="ts">
	import { DropdownMenu } from 'bits-ui';
	import DotsThree from 'phosphor-svelte/lib/DotsThree';
	import type { DropdownProps } from './dropdown.types.js';
	import { mapOffsetToBitsUI, mapPlacementToBitsUI } from '$lib/utils/placement.js';
	import { Placement } from '$lib/enums/placement.js';

	let {
		open = $bindable(false),
		children,
		buttonText,
		items,
		placement = Placement.BOTTOM_START,
		offset = { x: 0, y: 4 },
		contentProps,
		disabled = false,
		...restProps
	}: DropdownProps = $props();

	const _placement = $derived(mapPlacementToBitsUI(placement));
	const _offset = $derived(mapOffsetToBitsUI(offset.x, offset.y));
</script>

<DropdownMenu.Root bind:open {...restProps}>
	<div class="relative inline-block text-left">
		<DropdownMenu.Trigger
			{disabled}
			class="inline-flex w-full justify-center gap-x-1.5 {buttonText
				? 'rounded-lg px-3 py-2'
				: 'rounded-full p-2'} bg-background text-foreground shadow-mini ring-border-input text-sm font-semibold ring-1 transition-colors ring-inset {disabled
				? 'cursor-not-allowed opacity-50'
				: 'hover:bg-muted'}"
		>
			{#if buttonText}
				{buttonText}
				<svg
					class="text-foreground-alt -mr-1 size-5"
					viewBox="0 0 20 20"
					fill="currentColor"
					aria-hidden="true"
					data-slot="icon"
				>
					<path
						fill-rule="evenodd"
						d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
						clip-rule="evenodd"
					/>
				</svg>
			{:else}
				<DotsThree class="text-foreground-alt size-5" />
			{/if}</DropdownMenu.Trigger
		>
	</div>
	<DropdownMenu.Portal {disabled}>
		<DropdownMenu.Content
			{...contentProps}
			{..._placement}
			{..._offset}
			class="bg-background border-border-card shadow-popover w-[229px] rounded-xl border px-1 py-1.5 outline-hidden focus-visible:outline-hidden"
		>
			<DropdownMenu.Group aria-label={buttonText}>
				{#each items as item}
					<DropdownMenu.Item
						textValue={item}
						class="rounded-button data-highlighted:bg-muted text-foreground hover:bg-muted flex h-10 items-center py-3 pr-1.5 pl-3 text-sm font-medium ring-0! ring-transparent! transition-colors select-none focus-visible:outline-none"
					>
						<div class="flex items-center">
							{item}
						</div>
					</DropdownMenu.Item>
				{/each}
			</DropdownMenu.Group>
		</DropdownMenu.Content>
	</DropdownMenu.Portal>
</DropdownMenu.Root>
