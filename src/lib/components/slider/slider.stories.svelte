<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { Slider } from '$lib/components/slider/index.js';
	import { SelectionMode } from '$lib/enums/selection-mode.js';
	import { Orientation } from '$lib/enums/orientation.js';
	import { ComponentSize } from '$lib/enums/index.js';

	const { Story } = defineMeta({
		title: 'Components/Slider',
		component: Slider,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: [
					'type',
					'value',
					'min',
					'max',
					'step',
					'size',
					'orientation',
					'disabled',
					'showTicks',
					'showTickLabels'
				]
			}
		},
		argTypes: {
			type: {
				control: { type: 'select' },
				options: Object.values(SelectionMode),
				description: 'Single or multiple value selection'
			},
			value: {
				control: { type: 'object' },
				description: 'Current slider value(s)'
			},
			min: {
				control: { type: 'number' },
				description: 'Minimum value'
			},
			max: {
				control: { type: 'number' },
				description: 'Maximum value'
			},
			step: {
				control: { type: 'number' },
				description: 'Step increment'
			},
			size: {
				control: { type: 'select' },
				options: Object.values(ComponentSize),
				description: 'Slider size'
			},
			orientation: {
				control: { type: 'select' },
				options: Object.values(Orientation),
				description: 'Slider orientation'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the slider is disabled'
			},
			showTicks: {
				control: { type: 'boolean' },
				description: 'Whether to show tick marks'
			},
			showTickLabels: {
				control: { type: 'boolean' },
				description: 'Whether to show tick labels'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		type: SelectionMode.SINGLE,
		value: 50,
		min: 0,
		max: 100,
		step: 1,
		orientation: Orientation.HORIZONTAL,
		disabled: false,
		showTicks: false,
		showTickLabels: false
	}}
></Story>

<Story
	name="Multiple Values"
	args={{
		type: SelectionMode.MULTIPLE,
		value: [25, 75],
		min: 0,
		max: 100,
		step: 5,
		orientation: Orientation.HORIZONTAL,
		disabled: false,
		showTicks: false,
		showTickLabels: false
	}}
></Story>

<Story
	name="With Ticks"
	args={{
		type: SelectionMode.SINGLE,
		value: 30,
		min: 0,
		max: 100,
		step: 10,
		orientation: Orientation.HORIZONTAL,
		disabled: false,
		showTicks: true,
		showTickLabels: false
	}}
></Story>

<Story
	name="With Tick Labels"
	args={{
		type: SelectionMode.SINGLE,
		value: 40,
		min: 0,
		max: 100,
		step: 20,
		orientation: Orientation.HORIZONTAL,
		disabled: false,
		showTicks: true,
		showTickLabels: true
	}}
></Story>

<Story
	name="Disabled"
	args={{
		type: SelectionMode.SINGLE,
		value: 60,
		min: 0,
		max: 100,
		step: 1,
		orientation: Orientation.HORIZONTAL,
		disabled: true,
		showTicks: false,
		showTickLabels: false
	}}
></Story>

<Story
	name="Vertical with Labels"
	parameters={{
		layout: 'centered'
	}}
	args={{
		type: SelectionMode.SINGLE,
		value: 40,
		min: 0,
		max: 100,
		step: 20,
		orientation: Orientation.VERTICAL,
		disabled: false,
		showTicks: true,
		showTickLabels: true
	}}
></Story>
