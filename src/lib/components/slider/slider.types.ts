import type { ComponentSize } from '$lib/enums/component-size.js';
import type { Slider, WithoutChildren } from 'bits-ui';
import type { TickLabelPosition } from './slider.enums.js';

export type SliderProps = WithoutChildren<Slider.RootProps> & {
	class?: string;
	trackClass?: string;
	rangeClass?: string;
	thumbClass?: string;
	tickClass?: string;
	tickLabelClass?: string;
	showTicks?: boolean;
	showTickLabels?: boolean;
	size?: ComponentSize;
	tickLabelPosition?: TickLabelPosition;
};
