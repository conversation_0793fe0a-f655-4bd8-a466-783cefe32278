<!--
@component
Slider - A customizable slider component built on top of Bits UI Slider.

Allows users to select a value from a continuous range by sliding a handle. Supports both single
and multiple value selection with customizable appearance and behavior.

@see {@link https://www.bits-ui.com/docs/components/slider | Bits UI Slider Documentation}

@example
```svelte
<script lang="ts">
  import { Slider, SelectionMode } from '@tissue-dynamics/td-ui';

  let singleValue = $state(50);
  let multipleValues = $state([25, 75]);
</script>

<Slider
  type={SelectionMode.SINGLE}
  bind:value={singleValue}
  min={0}
  max={100}
  step={1}
/>

<Slider
  type={SelectionMode.MULTIPLE}
  bind:value={multipleValues}
  min={0}
  max={100}
  step={5}
  showTicks
/>
```
-->
<script lang="ts">
	import { Slider as BitsSlider } from 'bits-ui';
	import type { SliderProps } from './slider.types.js';
	import { SelectionMode } from '$lib/enums/selection-mode.js';
	import { Orientation } from '$lib/enums/orientation.js';
	import { ComponentSize } from '$lib/enums/index.js';
	import { sliderVariants } from './slider.styles.js';
	import { TickLabelPosition } from './slider.enums.js';

	let {
		value = $bindable(),
		ref = $bindable(null),
		type = SelectionMode.SINGLE,
		orientation = Orientation.HORIZONTAL,
		min = 0,
		max = 100,
		step = 1,
		disabled = false,
		showTicks = false,
		showTickLabels = false,
		tickLabelPosition = TickLabelPosition.TOP,
		class: className = '',
		trackClass = '',
		rangeClass = '',
		thumbClass = '',
		tickClass = '',
		tickLabelClass = '',
		size = ComponentSize.MD,
		...restProps
	}: SliderProps = $props();

	const rootClass = $derived(
		sliderVariants('root', {
			orientation,
			size,
			disabled,
			class: className
		})
	);

	const trackClassComputed = $derived(
		sliderVariants('track', {
			orientation,
			size,
			disabled,
			class: trackClass
		})
	);

	const rangeClassComputed = $derived(
		sliderVariants('range', {
			orientation,
			size,
			disabled,
			class: rangeClass
		})
	);

	const thumbClassComputed = $derived(
		sliderVariants('thumb', {
			size,
			disabled,
			class: thumbClass
		})
	);

	const tickClassComputed = $derived(
		sliderVariants('tick', {
			orientation,
			size,
			disabled,
			class: tickClass
		})
	);

	const tickLabelClassComputed = $derived(
		sliderVariants('tickLabel', {
			orientation,
			size,
			disabled,
			class: tickLabelClass
		})
	);

	const computedTickLabelPosition = $derived(
		orientation === Orientation.VERTICAL ? TickLabelPosition.LEFT : tickLabelPosition
	);
</script>

<BitsSlider.Root
	bind:value
	bind:ref
	{type}
	{orientation}
	{min}
	{max}
	{step}
	{disabled}
	class={rootClass}
	{...restProps as any}
>
	{#snippet children({ thumbItems, tickItems })}
		<span class={trackClassComputed}>
			<BitsSlider.Range class={rangeClassComputed} />
		</span>

		{#each thumbItems as { index } (index)}
			<BitsSlider.Thumb {index} class={thumbClassComputed} />
		{/each}

		{#if showTicks}
			{#each tickItems as { index, value } (index)}
				<BitsSlider.Tick {index} class={tickClassComputed} />
				{#if showTickLabels}
					<BitsSlider.TickLabel
						{index}
						position={computedTickLabelPosition}
						class={tickLabelClassComputed}
					>
						{value}
					</BitsSlider.TickLabel>
				{/if}
			{/each}
		{/if}
	{/snippet}
</BitsSlider.Root>
