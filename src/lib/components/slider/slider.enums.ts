export const SliderComponent = Object.freeze({
	ROOT: 'root',
	TRACK: 'track',
	RANGE: 'range',
	THUMB: 'thumb',
	TICK: 'tick',
	TICK_LABEL: 'tickLabel'
} as const);

export type SliderComponent = (typeof SliderComponent)[keyof typeof SliderComponent];

export const TickLabelPosition = Object.freeze({
	TOP: 'top',
	BOTTOM: 'bottom',
	LEFT: 'left',
	RIGHT: 'right'
} as const);

export type TickLabelPosition = (typeof TickLabelPosition)[keyof typeof TickLabelPosition];
