import { Orientation } from '$lib/enums/orientation.js';
import { ComponentSize } from '$lib/enums/index.js';
import type { SliderComponent } from './slider.enums.js';

const sliderStyles = {
	root: {
		base: ['relative', 'flex', 'touch-none', 'select-none'].join(' '),
		interactive: 'cursor-pointer',
		orientation: {
			[Orientation.HORIZONTAL]: 'w-full items-center',
			[Orientation.VERTICAL]: 'h-64 flex-col items-center'
		}
	},
	track: {
		base: ['relative', 'grow', 'overflow-hidden', 'rounded-full', 'bg-dark-10'].join(' '),
		interactive: 'cursor-pointer',
		orientation: {
			[Orientation.HORIZONTAL]: 'w-full',
			[Orientation.VERTICAL]: 'h-full'
		}
	},
	range: {
		base: 'absolute bg-foreground',
		orientation: {
			[Orientation.HORIZONTAL]: 'h-full',
			[Orientation.VERTICAL]: 'w-full'
		}
	},
	thumb: {
		base: [
			'block',
			'z-10',
			'border-border-input',
			'bg-background',
			'rounded-full',
			'border',
			'shadow-sm',
			'dark:bg-foreground',
			'dark:shadow-card',
			'focus-override',
			'focus-visible:outline-hidden',
			'data-active:scale-[0.98]',
			'disabled:pointer-events-none',
			'disabled:opacity-50',
			'transition-colors'
		].join(' '),
		interactive: 'cursor-pointer hover:border-dark-40'
	},
	tick: {
		base: 'z-1 dark:bg-background/20 bg-background',
		orientation: {
			[Orientation.HORIZONTAL]: 'w-[1px]',
			[Orientation.VERTICAL]: 'h-[1px]'
		}
	},
	tickLabel: {
		base: 'font-medium leading-none text-muted-foreground data-bounded:text-foreground',
		orientation: {
			[Orientation.HORIZONTAL]: '',
			[Orientation.VERTICAL]: 'mr-3'
		}
	}
} as const;

const trackSizeStyles = {
	[ComponentSize.SM]: {
		[Orientation.HORIZONTAL]: 'h-1 w-full mt-2',
		[Orientation.VERTICAL]: 'w-1 h-full'
	},
	[ComponentSize.MD]: {
		[Orientation.HORIZONTAL]: 'h-[6px] w-full mt-[10px]',
		[Orientation.VERTICAL]: 'w-[6px] h-full'
	},
	[ComponentSize.LG]: {
		[Orientation.HORIZONTAL]: 'h-[10px] w-full mt-3',
		[Orientation.VERTICAL]: 'w-[10px] h-full'
	}
};

const thumbSizeStyles = {
	[ComponentSize.SM]: 'size-[18px] mt-2',
	[ComponentSize.MD]: 'size-[22px] mt-[10px]',
	[ComponentSize.LG]: 'size-[28px] mt-3'
};

const tickSizeStyles = {
	[ComponentSize.SM]: {
		[Orientation.HORIZONTAL]: 'h-2 w-[1px] mt-1',
		[Orientation.VERTICAL]: 'w-2 h-[1px]'
	},
	[ComponentSize.MD]: {
		[Orientation.HORIZONTAL]: 'h-2 w-[1px] mt-2',
		[Orientation.VERTICAL]: 'w-2 h-[1px]'
	},
	[ComponentSize.LG]: {
		[Orientation.HORIZONTAL]: 'h-3 w-[1px] mt-3',
		[Orientation.VERTICAL]: 'w-3 h-[1px]'
	}
};

const tickLabelSizeStyles = {
	[ComponentSize.SM]: 'text-xs',
	[ComponentSize.MD]: 'text-sm',
	[ComponentSize.LG]: 'text-base'
};

export type SliderVariantConfig = {
	orientation?: Orientation;
	size?: ComponentSize;
	disabled?: boolean;
	class?: string;
};

export function sliderVariants(
	component: SliderComponent,
	config: SliderVariantConfig = {}
): string {
	const {
		orientation = Orientation.HORIZONTAL,
		size = ComponentSize.MD,
		disabled = false,
		class: additionalClasses = ''
	} = config;
	const styleConfig = sliderStyles[component];

	let sizeStyles = '';
	if (component === 'track' && trackSizeStyles[size]) {
		sizeStyles = trackSizeStyles[size][orientation];
	} else if (component === 'thumb' && thumbSizeStyles[size]) {
		sizeStyles = thumbSizeStyles[size];
	} else if (component === 'tick' && tickSizeStyles[size]) {
		sizeStyles = tickSizeStyles[size][orientation];
	} else if (component === 'tickLabel' && tickLabelSizeStyles[size]) {
		sizeStyles = tickLabelSizeStyles[size];
	}

	const classes = [
		styleConfig.base,
		'orientation' in styleConfig ? styleConfig.orientation[orientation] : undefined,
		sizeStyles,
		!disabled && 'interactive' in styleConfig && typeof styleConfig.interactive === 'string'
			? styleConfig.interactive
			: undefined,
		disabled && (component === 'root' || component === 'track')
			? 'opacity-50 cursor-not-allowed'
			: undefined,
		additionalClasses
	].filter(Boolean);

	return classes.join(' ');
}
