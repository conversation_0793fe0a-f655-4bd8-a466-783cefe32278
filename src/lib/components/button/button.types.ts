import type { Snippet } from 'svelte';
import type { Button } from 'bits-ui';
import type { ComponentSize } from '$lib/enums/index.js';
import { type ButtonVariant as ButtonVariantType } from './button.enums.js';

export type ButtonVariantConfig = {
	variant?: ButtonVariantType;
	size?: ComponentSize;
	class?: string;
};

export type ButtonProps = Button.RootProps & {
	children: Snippet;
	variant?: ButtonVariantType;
	size?: ComponentSize;
	loading?: boolean;
	class?: string;
};
