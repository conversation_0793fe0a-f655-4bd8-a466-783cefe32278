<!--
@component
Button - A versatile button component built on top of Bits UI Button.

Provides interactive elements for triggering actions with loading states, multiple variants,
and size options. Can render as button or anchor element.

@see {@link https://www.bits-ui.com/docs/components/button | Bits UI Button Documentation}

@example
```svelte
<script lang="ts">
  import { Button, ButtonVariant, ComponentSize } from '@tissue-dynamics/td-ui';

  let isLoading = $state(false);
</script>

<Button variant={ButtonVariant.PRIMARY} onclick={() => console.log('clicked')}>
  Click me
</Button>

<Button variant={ButtonVariant.OUTLINE} href="/dashboard">
  Go to Dashboard
</Button>

<Button variant={ButtonVariant.DESTRUCTIVE} bind:loading={isLoading}>
  {isLoading ? 'Processing...' : 'Delete Item'}
</Button>
```
-->
<script lang="ts">
	import { Button as BitsButton } from 'bits-ui';
	import type { ButtonProps } from './button.types.js';
	import { ButtonVariant } from './button.enums.js';
	import { ComponentSize } from '$lib/enums/index.js';
	import { buttonVariants } from './button.styles.js';

	let {
		children,
		variant = ButtonVariant.DEFAULT,
		size = ComponentSize.MD,
		loading = $bindable(false),
		class: className = '',
		...bitsProps
	}: ButtonProps = $props();

	const computedClass = $derived(
		buttonVariants({
			variant,
			size,
			class: className
		})
	);

	const isDisabled = $derived(bitsProps.disabled || loading);
</script>

<BitsButton.Root
	class={computedClass}
	disabled={isDisabled}
	data-loading={loading}
	data-variant={variant}
	data-size={size}
	{...bitsProps}
>
	{#if loading}
		<svg
			class="mr-2 h-4 w-4 animate-spin"
			xmlns="http://www.w3.org/2000/svg"
			fill="none"
			viewBox="0 0 24 24"
		>
			<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
			<path
				class="opacity-75"
				fill="currentColor"
				d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
			/>
		</svg>
	{/if}
	{@render children()}
</BitsButton.Root>
