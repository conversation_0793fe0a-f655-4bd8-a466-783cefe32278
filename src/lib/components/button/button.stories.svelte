<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { Button, ButtonVariant } from '$lib/components/button/index.js';
	import { ComponentSize } from '$lib/enums/index.js';

	const { Story } = defineMeta({
		title: 'Components/Button',
		component: Button,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['variant', 'size', 'loading', 'disabled', 'href', 'onclick']
			},
			layout: 'centered'
		},
		argTypes: {
			variant: {
				control: { type: 'select' },
				options: Object.values(ButtonVariant),
				description: 'The visual style variant of the button'
			},
			size: {
				control: { type: 'select' },
				options: Object.values(ComponentSize),
				description: 'The size of the button'
			},
			loading: {
				control: { type: 'boolean' },
				description: 'Whether the button is in a loading state'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the button is disabled'
			},
			href: {
				control: { type: 'text' },
				description:
					'When provided, renders the button as an anchor tag instead of a button element'
			},
			onclick: {
				action: 'click',
				description: 'Callback when the button is clicked'
			}
		}
	});

	function handleClick() {
		console.log('Button clicked!');
	}
</script>

<Story
	name="Default"
	args={{
		variant: ButtonVariant.DEFAULT,
		size: ComponentSize.MD,
		onclick: handleClick
	}}
>
	Default Button
</Story>

<Story
	name="Primary"
	args={{
		variant: ButtonVariant.PRIMARY,
		size: ComponentSize.MD,
		onclick: handleClick
	}}
>
	Primary Button
</Story>

<Story
	name="Secondary"
	args={{
		variant: ButtonVariant.SECONDARY,
		size: ComponentSize.MD,
		onclick: handleClick
	}}
>
	Secondary Button
</Story>

<Story
	name="Outline"
	args={{
		variant: ButtonVariant.OUTLINE,
		size: ComponentSize.MD,
		onclick: handleClick
	}}
>
	Outline Button
</Story>

<Story
	name="Ghost"
	args={{
		variant: ButtonVariant.GHOST,
		size: ComponentSize.MD,
		onclick: handleClick
	}}
>
	Ghost Button
</Story>

<Story
	name="Destructive"
	args={{
		variant: ButtonVariant.DESTRUCTIVE,
		size: ComponentSize.MD,
		onclick: handleClick
	}}
>
	Destructive Button
</Story>

<Story
	name="Link"
	args={{
		variant: ButtonVariant.LINK,
		size: ComponentSize.MD,
		onclick: handleClick
	}}
>
	Link Button
</Story>

<Story
	name="Loading State"
	args={{
		variant: ButtonVariant.PRIMARY,
		size: ComponentSize.MD,
		loading: true,
		onclick: handleClick
	}}
>
	Loading
</Story>

<Story
	name="Disabled"
	args={{
		variant: ButtonVariant.PRIMARY,
		size: ComponentSize.MD,
		disabled: true,
		onclick: handleClick
	}}
>
	Disabled
</Story>

<Story
	name="As Link"
	args={{
		variant: ButtonVariant.OUTLINE,
		size: ComponentSize.MD,
		href: '/dashboard'
	}}
>
	Go to Dashboard
</Story>
