import type { ButtonVariantConfig } from './button.types.js';
import { ButtonVariant } from './button.enums.js';
import { ComponentSize } from '$lib/enums/index.js';
const baseStyles = [
	// Layout & Display
	'inline-flex',
	'items-center',
	'justify-center',
	'gap-2',

	// Typography
	'font-medium',
	'text-sm',
	'whitespace-nowrap',

	// Interaction
	'cursor-pointer',
	'select-none',

	// Transitions
	'transition-all',
	'duration-200',
	'ease-in-out',

	// Focus
	'focus-visible:outline-none',
	'focus-visible:ring-2',
	'focus-visible:ring-offset-2',

	// Disabled state
	'disabled:cursor-not-allowed',
	'disabled:opacity-50',

	// Reset any default shadows/elevation
	'shadow-none',
	'!shadow-none',

	// Border radius
	'rounded-md'
].join(' ');

const variantStyles = {
	[ButtonVariant.DEFAULT]: [
		'bg-gray-100',
		'text-gray-900',
		'border',
		'border-gray-300',
		'hover:bg-gray-200',
		'focus-visible:ring-gray-500'
	].join(' '),

	[ButtonVariant.PRIMARY]: [
		'bg-blue-600',
		'text-white',
		'border',
		'border-blue-600',
		'hover:bg-blue-700',
		'hover:border-blue-700',
		'focus-visible:ring-blue-500'
	].join(' '),

	[ButtonVariant.SECONDARY]: [
		'bg-gray-600',
		'text-white',
		'border',
		'border-gray-600',
		'hover:bg-gray-700',
		'hover:border-gray-700',
		'focus-visible:ring-gray-500'
	].join(' '),

	[ButtonVariant.OUTLINE]: [
		'bg-transparent',
		'text-gray-900',
		'border',
		'border-gray-300',
		'hover:bg-gray-50',
		'focus-visible:ring-gray-500'
	].join(' '),

	[ButtonVariant.GHOST]: [
		'bg-transparent',
		'text-gray-900',
		'border',
		'border-transparent',
		'hover:bg-gray-100',
		'focus-visible:ring-gray-500'
	].join(' '),

	[ButtonVariant.DESTRUCTIVE]: [
		'bg-red-600',
		'text-white',
		'border',
		'border-red-600',
		'hover:bg-red-700',
		'hover:border-red-700',
		'focus-visible:ring-red-500'
	].join(' '),

	[ButtonVariant.LINK]: [
		'bg-transparent',
		'text-blue-600',
		'underline-offset-4',
		'hover:underline',
		'focus-visible:ring-blue-500',
		'p-0',
		'h-auto'
	].join(' ')
};

const sizeStyles = {
	[ComponentSize.SM]: 'h-8 px-3 text-sm',
	[ComponentSize.MD]: 'h-9 px-4 text-sm',
	[ComponentSize.LG]: 'h-10 px-6 text-base'
};

export function buttonVariants(config: ButtonVariantConfig = {}): string {
	const {
		variant = ButtonVariant.DEFAULT,
		size = ComponentSize.MD,
		class: additionalClasses = ''
	} = config;

	const classes = [
		baseStyles,
		variantStyles[variant],
		variant !== ButtonVariant.LINK ? sizeStyles[size] : '',
		additionalClasses
	]
		.filter(Boolean)
		.join(' ');

	return classes;
}
