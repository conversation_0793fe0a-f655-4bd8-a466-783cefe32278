import type { Placement } from '$lib/enums/placement.js';
import type { Tooltip, WithoutChildren } from 'bits-ui';
import type { Snippet } from 'svelte';

export type TooltipProps = WithoutChildren<Tooltip.RootProps> & {
	children: Snippet;
	content: string;
	placement?: Placement;
	offset?: { x: number; y: number };
	showArrow?: boolean;
	triggerProps?: WithoutChildren<Tooltip.TriggerProps>;
	portalProps?: WithoutChildren<Tooltip.PortalProps>;
	contentProps?: Omit<
		WithoutChildren<Tooltip.ContentProps>,
		'side' | 'align' | 'sideOffset' | 'alignOffset'
	>;
	arrowProps?: WithoutChildren<Tooltip.ArrowProps>;
};
