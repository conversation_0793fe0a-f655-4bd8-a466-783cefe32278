<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import Tooltip from '$lib/components/tooltip/tooltip.svelte';
	import { Placement } from '$lib/enums/placement.js';

	const { Story } = defineMeta({
		title: 'Components/Tooltip',
		component: Tooltip,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['content', 'placement', 'showArrow', 'delayDuration']
			},
			layout: 'centered'
		},

		argTypes: {
			content: {
				control: 'text',
				description: 'The content to display in the tooltip'
			},
			placement: {
				control: 'select',
				options: Object.values(Placement),
				description: 'The placement of the tooltip relative to the trigger'
			},
			showArrow: {
				control: 'boolean',
				description: 'Whether to show an arrow pointing to the trigger'
			},
			delayDuration: {
				control: 'number',
				description: 'The delay in milliseconds before the tooltip appears'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		content: 'This is a helpful tooltip',
		placement: Placement.TOP,
		showArrow: false,
		delayDuration: 200
	}}
>
	<span
		class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
	>
		Hover me
	</span>
</Story>

<Story
	name="With Arrow"
	args={{
		content: 'Tooltip with arrow',
		placement: Placement.BOTTOM,
		showArrow: true,
		delayDuration: 200
	}}
>
	<span
		class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
	>
		Hover for arrow
	</span>
</Story>

<Story
	name="Different Placements"
	args={{
		content: 'Top tooltip',
		placement: Placement.TOP,
		showArrow: false,
		delayDuration: 200
	}}
>
	<span
		class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
	>
		Top
	</span>
</Story>

<Story
	name="Fast Delay"
	args={{
		content: 'Quick tooltip with no delay',
		placement: Placement.TOP,
		showArrow: false,
		delayDuration: 0
	}}
>
	<span
		class="bg-foreground text-background hover:bg-foreground/90 inline-flex h-10 cursor-pointer items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors"
	>
		Instant tooltip
	</span>
</Story>
