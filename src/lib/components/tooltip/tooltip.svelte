<!--
@component
Tooltip - A tooltip component built on top of Bits UI Tooltip.

Displays helpful information when users hover over or focus on an element.
Requires a TooltipProvider to be wrapped around your app or layout.

@see {@link https://www.bits-ui.com/docs/components/tooltip | Bits UI Tooltip Documentation}

@example
```svelte
<script lang="ts">
  import { Tooltip, TooltipProvider } from '@tissue-dynamics/td-ui';
</script>

<TooltipProvider>
  <Tooltip content="This is a helpful tooltip">
    <button>Hover me</button>
  </Tooltip>
</TooltipProvider>

<TooltipProvider>
  <Tooltip content="Save your changes" showArrow={true}>
    <button>Save</button>
  </Tooltip>
</TooltipProvider>
```
-->
<script lang="ts">
	import { Tooltip } from 'bits-ui';
	import type { TooltipProps } from './tooltip.types.js';
	import { Placement } from '$lib/enums/placement.js';
	import { mapOffsetToBitsUI, mapPlacementToBitsUI } from '$lib/utils/placement.js';

	let {
		open = $bindable(false),
		children,
		content,
		placement = Placement.TOP,
		offset = { x: 0, y: 4 },
		showArrow = false,
		delayDuration = 200,
		...restProps
	}: TooltipProps = $props();

	const _placement = $derived(mapPlacementToBitsUI(placement));
	const _offset = $derived(mapOffsetToBitsUI(offset.x, offset.y));
</script>

<Tooltip.Root bind:open {delayDuration} {...restProps}>
	<Tooltip.Trigger>
		{@render children()}
	</Tooltip.Trigger>
	<Tooltip.Portal>
		<Tooltip.Content
			{..._placement}
			{..._offset}
			class="bg-foreground text-background data-[state=delayed-open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=delayed-open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=delayed-open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 shadow-popover border-border-card z-[9999] overflow-hidden rounded-md border px-3 py-1.5 text-xs"
		>
			{content}
			{#if showArrow}
				<Tooltip.Arrow class="fill-foreground" />
			{/if}
		</Tooltip.Content>
	</Tooltip.Portal>
</Tooltip.Root>
