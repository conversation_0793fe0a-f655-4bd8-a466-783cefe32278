<!--
@component
Calendar - A date picker component built on top of Bits UI DatePicker.

Provides date selection interface with keyboard navigation, date constraints, and accessibility support.
Includes an integrated input field with optional trigger button.

@see {@link https://www.bits-ui.com/docs/components/date-picker | Bits UI DatePicker Documentation}

@example
```svelte
<script lang="ts">
  import { Calendar } from '@tissue-dynamics/td-ui';
  import { CalendarDate, type DateValue } from '@internationalized/date';

  let selectedDate = $state<DateValue>();
</script>

<Calendar
  bind:value={selectedDate}
  label="Select Date"
  showTrigger={true}
/>

<Calendar
  bind:value={selectedDate}
  label="Birthday"
  minValue={new CalendarDate(1900, 1, 1)}
  maxValue={new CalendarDate(2030, 12, 31)}
/>
```
-->
<script lang="ts">
	import { DatePicker } from 'bits-ui';
	import type { CalendarProps } from './calendar.types.js';
	import { CalendarBlank, CaretLeft, CaretRight } from 'phosphor-svelte';
	import { Placement } from '$lib/enums/placement.js';
	import { mapOffsetToBitsUI, mapPlacementToBitsUI } from '$lib/utils/placement.js';

	let {
		value = $bindable(),
		label,
		placement = Placement.BOTTOM,
		offset = { x: 0, y: 6 },
		contentProps,
		inputProps,
		showTrigger = true,
		containerClass = '',
		inputClass = '',
		contentClass = '',
		disabled = false,
		...restProps
	}: CalendarProps = $props();

	const _placement = $derived(mapPlacementToBitsUI(placement));
	const _offset = $derived(mapOffsetToBitsUI(offset.x, offset.y));
</script>

<DatePicker.Root bind:value weekdayFormat="short" fixedWeeks={true} {disabled} {...restProps}>
	<div class="flex w-full max-w-[296px] flex-col gap-1.5 {containerClass}">
		{#if label}
			<DatePicker.Label class="text-foreground block text-sm font-medium select-none">
				{label}
			</DatePicker.Label>
		{/if}

		<DatePicker.Input
			{...inputProps}
			class="h-input rounded-9px border-border-input bg-background text-foreground focus-within:border-border-input-hover focus-within:shadow-date-field-focus hover:border-border-input-hover flex w-full items-center border px-3 py-3 text-sm tracking-[0.01em] transition-colors select-none {disabled
				? 'cursor-not-allowed opacity-50'
				: ''} {inputClass}"
		>
			{#snippet children({ segments })}
				{#each segments as { part, value }, i (part + i)}
					<div class="inline-block select-none">
						{#if part === 'literal'}
							<DatePicker.Segment {part} class="text-muted-foreground p-1">
								{value}
							</DatePicker.Segment>
						{:else}
							<DatePicker.Segment
								{part}
								class="rounded-5px hover:bg-muted focus:bg-muted focus:text-foreground aria-[valuetext=Empty]:text-muted-foreground px-1 py-1 transition-colors focus-visible:ring-0! focus-visible:ring-offset-0!"
							>
								{value}
							</DatePicker.Segment>
						{/if}
					</div>
				{/each}

				{#if showTrigger}
					<DatePicker.Trigger
						{disabled}
						class="text-foreground/60 hover:bg-muted active:bg-muted/80 rounded-5px ml-auto inline-flex size-8 items-center justify-center transition-all {disabled
							? 'cursor-not-allowed opacity-50'
							: ''}"
					>
						<CalendarBlank class="size-5" />
					</DatePicker.Trigger>
				{/if}
			{/snippet}
		</DatePicker.Input>

		<DatePicker.Portal>
			<DatePicker.Content
				{...contentProps}
				{..._placement}
				{..._offset}
				class="focus-override border-muted bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 shadow-popover rounded-15px z-50 border outline-hidden select-none data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1 {contentClass}"
			>
				<DatePicker.Calendar class="p-6">
					{#snippet children({ months, weekdays })}
						<DatePicker.Header class="mb-4 flex items-center justify-between">
							<DatePicker.PrevButton
								{disabled}
								class="rounded-9px bg-background hover:bg-muted border-border-input inline-flex size-10 items-center justify-center border transition-all active:scale-[0.98] {disabled
									? 'cursor-not-allowed opacity-50'
									: ''}"
							>
								<CaretLeft class="size-5" />
							</DatePicker.PrevButton>
							<DatePicker.Heading class="text-foreground text-[15px] font-medium" />
							<DatePicker.NextButton
								{disabled}
								class="rounded-9px bg-background hover:bg-muted border-border-input inline-flex size-10 items-center justify-center border transition-all active:scale-[0.98] {disabled
									? 'cursor-not-allowed opacity-50'
									: ''}"
							>
								<CaretRight class="size-5" />
							</DatePicker.NextButton>
						</DatePicker.Header>

						<div class="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
							{#each months as month (month.value)}
								<DatePicker.Grid class="w-full border-collapse space-y-1 select-none">
									<DatePicker.GridHead>
										<DatePicker.GridRow class="mb-2 flex w-full justify-between">
											{#each weekdays as day (day)}
												<DatePicker.HeadCell
													class="text-muted-foreground w-10 rounded-md text-center text-xs font-normal"
												>
													<div>{day.slice(0, 2)}</div>
												</DatePicker.HeadCell>
											{/each}
										</DatePicker.GridRow>
									</DatePicker.GridHead>
									<DatePicker.GridBody>
										{#each month.weeks as weekDates (weekDates)}
											<DatePicker.GridRow class="flex w-full">
												{#each weekDates as date (date)}
													<DatePicker.Cell
														{date}
														month={month.value}
														class="relative size-10 p-0 text-center text-sm"
													>
														<DatePicker.Day
															class="rounded-9px text-foreground hover:bg-muted data-selected:bg-foreground data-disabled:text-foreground/30 data-selected:text-background data-unavailable:text-muted-foreground group data-outside-month:text-muted-foreground/50 relative inline-flex size-10 items-center justify-center border border-transparent bg-transparent p-0 text-sm font-normal whitespace-nowrap transition-all data-disabled:pointer-events-none data-outside-month:pointer-events-none data-selected:font-medium data-unavailable:line-through"
														>
															<div
																class="bg-foreground group-data-selected:bg-background absolute top-[5px] hidden size-1 rounded-full transition-all group-data-today:block"
															></div>
															{date.day}
														</DatePicker.Day>
													</DatePicker.Cell>
												{/each}
											</DatePicker.GridRow>
										{/each}
									</DatePicker.GridBody>
								</DatePicker.Grid>
							{/each}
						</div>
					{/snippet}
				</DatePicker.Calendar>
			</DatePicker.Content>
		</DatePicker.Portal>
	</div>
</DatePicker.Root>
