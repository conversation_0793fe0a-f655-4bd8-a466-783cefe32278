<script module lang="ts">
	import { defineMeta } from '@storybook/addon-svelte-csf';
	import { Calendar } from '$lib/components/calendar/index.js';
	import { Placement } from '$lib/enums/placement.js';
	import { CalendarDate } from '@internationalized/date';

	const { Story } = defineMeta({
		title: 'Components/Calendar',
		component: Calendar,
		tags: ['autodocs'],
		parameters: {
			controls: {
				include: ['label', 'disabled', 'showTrigger', 'placement']
			},
			layout: 'centered'
		},
		argTypes: {
			label: {
				control: { type: 'text' },
				description: 'The label text for the calendar input'
			},
			disabled: {
				control: { type: 'boolean' },
				description: 'Whether the calendar is disabled'
			},
			showTrigger: {
				control: { type: 'boolean' },
				description: 'Whether to show the calendar trigger button'
			},
			placement: {
				control: { type: 'select' },
				options: Object.values(Placement),
				description: 'The placement of the calendar popover'
			}
		}
	});
</script>

<Story
	name="Default"
	args={{
		label: 'Select Date',
		showTrigger: true
	}}
></Story>

<Story
	name="Without Label"
	args={{
		showTrigger: true
	}}
></Story>

<Story
	name="Without Trigger"
	args={{
		label: 'Date Input',
		showTrigger: false
	}}
></Story>

<Story
	name="Disabled"
	args={{
		label: 'Disabled Calendar',
		disabled: true,
		showTrigger: true
	}}
></Story>

<Story
	name="With Date Constraints"
	args={{
		label: 'Constrained Calendar',
		showTrigger: true,
		minValue: new CalendarDate(2026, 1, 1),
		maxValue: new CalendarDate(2026, 12, 31)
	}}
></Story>
