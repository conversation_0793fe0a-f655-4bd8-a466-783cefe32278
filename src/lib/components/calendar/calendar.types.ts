import type { Placement } from '$lib/enums/placement.js';
import type { DatePicker, WithoutChildren } from 'bits-ui';

export type CalendarProps = WithoutChildren<DatePicker.RootProps> & {
	label?: string;
	placement?: Placement;
	offset?: { x: number; y: number };
	contentProps?: Omit<
		WithoutChildren<DatePicker.ContentProps>,
		'side' | 'align' | 'sideOffset' | 'alignOffset'
	>;
	inputProps?: WithoutChildren<DatePicker.InputProps>;
	showTrigger?: boolean;
	containerClass?: string;
	inputClass?: string;
	contentClass?: string;
	disabled?: boolean;
};
