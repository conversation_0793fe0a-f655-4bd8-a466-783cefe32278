<script>
	import { TooltipProvider, Tooltip } from '$lib/index.js';
</script>

<TooltipProvider delayDuration={0}>
	<div style="padding: 50px; text-align: center;">
		<h1>Custom Tooltip Test</h1>

		<!-- Test our custom tooltip -->
		<Tooltip content="This is our custom tooltip!">
			<span style="background: red; color: white; padding: 10px; cursor: pointer;">
				Hover me (custom)!
			</span>
		</Tooltip>

		<br /><br />

		<!-- Test with span (no button nesting) -->
		<Tooltip content="Span tooltip" showArrow={true}>
			<span
				style="background: blue; color: white; padding: 10px; border: none; border-radius: 4px; cursor: pointer; display: inline-block;"
			>
				Span with tooltip
			</span>
		</Tooltip>

		<br /><br />

		<!-- Test with different placement -->
		<Tooltip content="Bottom tooltip" placement="bottom">
			<div
				style="background: green; color: white; padding: 10px; cursor: pointer; display: inline-block;"
			>
				Bottom tooltip
			</div>
		</Tooltip>
	</div>
</TooltipProvider>
