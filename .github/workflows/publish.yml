name: Publish Package

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with:
          version: 8
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: https://npm.pkg.github.com
          scope: '@tissue-dynamics'
      - run: pnpm install
      - run: pnpm build
      - run: pnpm install -g changesets
      - run: pnpm changeset publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.PUBLISH_TOKEN }}
