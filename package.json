{"name": "@tissue-dynamics/td-ui", "version": "0.0.2", "scripts": {"dev": "vite dev", "build": "vite build && npm run prepack", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "prepack": "svelte-kit sync && svelte-package && publint", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean:kit": "rm -rf .svelte-kit"}, "files": ["dist", "!dist/**/*.test.*", "!dist/**/*.spec.*"], "sideEffects": ["**/*.css"], "svelte": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "svelte": "./dist/index.js"}}, "peerDependencies": {"svelte": "^5.0.0"}, "devDependencies": {"@changesets/cli": "^2.29.5", "@chromatic-com/storybook": "^4.0.1", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@playwright/test": "^1.49.1", "@storybook/addon-a11y": "^9.0.14", "@storybook/addon-docs": "^9.0.14", "@storybook/addon-svelte-csf": "^5.0.4", "@storybook/addon-vitest": "^9.0.14", "@storybook/sveltekit": "^9.0.14", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/package": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "@types/node": "^22", "@vitest/browser": "^3.2.3", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-storybook": "^9.0.14", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "playwright": "^1.53.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "publint": "^0.3.2", "storybook": "^9.0.14", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "vitest": "^3.2.3", "vitest-browser-svelte": "^0.1.0"}, "keywords": ["svelte"], "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "https://github.com/Tissue-Dynamics/td-ui.git"}, "dependencies": {"@internationalized/date": "^3.8.2", "bits-ui": "^2.8.10", "phosphor-svelte": "^3.0.1"}}